{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "dpr": "node app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@firebasegen/default-connector": "file:dataconnect-generated/js/default-connector", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "http": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "loadash": "^1.0.0", "lodash": "^4.17.21", "ml-matrix": "^6.12.0", "ml-random-forest": "^2.1.0", "mongoose": "^8.9.2", "multer": "^1.4.5-lts.1", "nodemon": "^3.1.9", "shortid": "^2.2.17", "socket.io": "^4.8.1", "uuid": "^11.1.0"}}