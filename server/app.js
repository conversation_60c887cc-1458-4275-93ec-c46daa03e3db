const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const app = express();

// 🔧 Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 📁 Static files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 🗄️ MongoDB Connection
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_CONNECTION_STRING || 'mongodb://localhost:27017/ims';
    
    await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ MongoDB connected successfully');
    console.log(`📊 Database: ${mongoose.connection.db.databaseName}`);
    
    // Create indexes for better performance
    await createIndexes();
    
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// 📊 Create database indexes
const createIndexes = async () => {
  try {
    const DPR = require('./models/DPR');
    
    // Ensure geospatial index exists
    await DPR.collection.createIndex({ 'gpsLocation.location': '2dsphere' });
    
    // Ensure text search index exists
    await DPR.collection.createIndex({
      workDescription: 'text',
      location: 'text',
      contractorName: 'text',
      remarks: 'text'
    });
    
    console.log('📊 Database indexes created successfully');
  } catch (error) {
    console.warn('⚠️ Warning: Could not create indexes:', error.message);
  }
};

// 🛣️ Routes
const dprRoutes = require('./routes/dpr');
app.use('/api/dpr', dprRoutes);

// 🏠 Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'DPR API Server is running',
    timestamp: new Date().toISOString(),
    database: mongoose.connection.readyState === 1 ? 'Connected' : 'Disconnected'
  });
});

// 📊 Database stats endpoint
app.get('/api/stats', async (req, res) => {
  try {
    const DPR = require('./models/DPR');
    
    const stats = await DPR.aggregate([
      {
        $group: {
          _id: null,
          totalReports: { $sum: 1 },
          totalAttachments: { $sum: { $size: '$attachments' } },
          avgAttachmentsPerReport: { $avg: { $size: '$attachments' } },
          reportsWithGPS: {
            $sum: {
              $cond: [{ $ne: ['$gpsLocation', null] }, 1, 0]
            }
          },
          uniqueContractors: { $addToSet: '$contractorName' },
          uniqueLocations: { $addToSet: '$location' },
          statusBreakdown: { $push: '$status' }
        }
      },
      {
        $project: {
          totalReports: 1,
          totalAttachments: 1,
          avgAttachmentsPerReport: { $round: ['$avgAttachmentsPerReport', 2] },
          reportsWithGPS: 1,
          uniqueContractors: { $size: '$uniqueContractors' },
          uniqueLocations: { $size: '$uniqueLocations' },
          gpsPercentage: {
            $round: [
              { $multiply: [{ $divide: ['$reportsWithGPS', '$totalReports'] }, 100] },
              1
            ]
          }
        }
      }
    ]);

    res.json({
      success: true,
      data: stats[0] || {
        totalReports: 0,
        totalAttachments: 0,
        avgAttachmentsPerReport: 0,
        reportsWithGPS: 0,
        uniqueContractors: 0,
        uniqueLocations: 0,
        gpsPercentage: 0
      }
    });
  } catch (error) {
    console.error('Error fetching stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch statistics',
      error: error.message
    });
  }
});

// 🔍 Search endpoint
app.get('/api/search', async (req, res) => {
  try {
    const { q, type = 'all' } = req.query;
    
    if (!q) {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
    }

    const DPR = require('./models/DPR');
    let searchFilter = {};

    switch (type) {
      case 'location':
        searchFilter = { location: new RegExp(q, 'i') };
        break;
      case 'contractor':
        searchFilter = { contractorName: new RegExp(q, 'i') };
        break;
      case 'work':
        searchFilter = { workDescription: new RegExp(q, 'i') };
        break;
      default:
        searchFilter = { $text: { $search: q } };
    }

    const results = await DPR.find(searchFilter)
      .select('-attachments.data')
      .populate('createdBy', 'name email')
      .limit(20)
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  } catch (error) {
    console.error('Error searching:', error);
    res.status(500).json({
      success: false,
      message: 'Search failed',
      error: error.message
    });
  }
});

// 🌍 Geospatial search endpoint
app.get('/api/nearby', async (req, res) => {
  try {
    const { lat, lng, radius = 1000 } = req.query;
    
    if (!lat || !lng) {
      return res.status(400).json({
        success: false,
        message: 'Latitude and longitude are required'
      });
    }

    const DPR = require('./models/DPR');
    
    const nearbyReports = await DPR.find({
      'gpsLocation.location': {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [parseFloat(lng), parseFloat(lat)]
          },
          $maxDistance: parseInt(radius)
        }
      }
    })
    .select('-attachments.data')
    .populate('createdBy', 'name email')
    .limit(50);

    res.json({
      success: true,
      data: nearbyReports,
      count: nearbyReports.length,
      searchCenter: {
        latitude: parseFloat(lat),
        longitude: parseFloat(lng),
        radius: parseInt(radius)
      }
    });
  } catch (error) {
    console.error('Error finding nearby reports:', error);
    res.status(500).json({
      success: false,
      message: 'Geospatial search failed',
      error: error.message
    });
  }
});

// 🚫 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found',
    path: req.originalUrl
  });
});

// 🚨 Error handler
app.use((error, req, res, next) => {
  console.error('Server Error:', error);
  
  // Multer file upload errors
  if (error.code === 'LIMIT_FILE_SIZE') {
    return res.status(400).json({
      success: false,
      message: 'File too large. Maximum size is 10MB per file.'
    });
  }
  
  if (error.code === 'LIMIT_FILE_COUNT') {
    return res.status(400).json({
      success: false,
      message: 'Too many files. Maximum 20 files per request.'
    });
  }

  // MongoDB errors
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      errors: Object.values(error.errors).map(e => e.message)
    });
  }

  if (error.name === 'CastError') {
    return res.status(400).json({
      success: false,
      message: 'Invalid ID format'
    });
  }

  // Generic error
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// 🚀 Start server
const PORT = process.env.PORT || 5017;

const startServer = async () => {
  try {
    await connectDB();
    
    app.listen(PORT, () => {
      console.log(`🚀 DPR API Server running on port ${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
      console.log(`📈 Statistics: http://localhost:${PORT}/api/stats`);
      console.log(`🔍 Search: http://localhost:${PORT}/api/search?q=excavation`);
      console.log(`🌍 Nearby: http://localhost:${PORT}/api/nearby?lat=17.4486&lng=78.3908`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down server...');
  await mongoose.connection.close();
  console.log('📊 Database connection closed');
  process.exit(0);
});

startServer();

module.exports = app;
