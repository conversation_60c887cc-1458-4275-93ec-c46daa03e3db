const express = require('express');
const multer = require('multer');
const DPR = require('../models/DPR');
const router = express.Router();

// 📁 Configure multer for file uploads (store in memory as Buffer)
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit per file
    files: 20 // Maximum 20 files per request
  },
  fileFilter: (req, file, cb) => {
    // Allow images, videos, and documents
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'video/mp4', 'video/avi', 'video/mov',
      'application/pdf', 'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only images, videos, and documents are allowed.'), false);
    }
  }
});

// 📋 GET /api/dpr - Get all DPR reports with filtering
router.get('/', async (req, res) => {
  try {
    const {
      date,
      location,
      contractor,
      status,
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build filter object
    const filter = {};
    if (date) filter.date = new Date(date);
    if (location) filter.location = new RegExp(location, 'i');
    if (contractor) filter.contractorName = new RegExp(contractor, 'i');
    if (status) filter.status = status;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    // Execute query
    const reports = await DPR.find(filter)
      .select('-attachments.data') // Exclude binary data for list view
      .populate('createdBy', 'name email')
      .populate('approvedBy', 'name email')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await DPR.countDocuments(filter);

    res.json({
      success: true,
      data: reports,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / parseInt(limit)),
        total,
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching DPR reports:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch DPR reports',
      error: error.message
    });
  }
});

// 📋 GET /api/dpr/:id - Get single DPR report with all data
router.get('/:id', async (req, res) => {
  try {
    const report = await DPR.findById(req.params.id)
      .populate('createdBy', 'name email')
      .populate('approvedBy', 'name email');

    if (!report) {
      return res.status(404).json({
        success: false,
        message: 'DPR report not found'
      });
    }

    res.json({
      success: true,
      data: report
    });
  } catch (error) {
    console.error('Error fetching DPR report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch DPR report',
      error: error.message
    });
  }
});

// 📸 GET /api/dpr/:id/attachment/:attachmentId - Get specific attachment
router.get('/:id/attachment/:attachmentId', async (req, res) => {
  try {
    const report = await DPR.findById(req.params.id);
    if (!report) {
      return res.status(404).json({
        success: false,
        message: 'DPR report not found'
      });
    }

    const attachment = report.attachments.id(req.params.attachmentId);
    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    // Set appropriate headers
    res.set({
      'Content-Type': attachment.mimeType,
      'Content-Length': attachment.size,
      'Content-Disposition': `inline; filename="${attachment.originalName}"`
    });

    // Send binary data
    res.send(attachment.data);
  } catch (error) {
    console.error('Error fetching attachment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch attachment',
      error: error.message
    });
  }
});

// 📝 POST /api/dpr - Create new DPR report
router.post('/', upload.array('attachments', 20), async (req, res) => {
  try {
    const {
      date, sNo, contractorName, agencyName, location, workDescription,
      node, pipeDiameter, units, engineerSupervisor, remarks,
      hdpeExcavation, hdpePipeLaying, hdpeBackFilling,
      msExcavation, msPipeLaying, msBackFilling,
      targetQty, lastSeasonQty, thisSeasonQty, cumulativeQty, balanceQty,
      skilledLabour, unskilledLabour, concreteQty, machineryDetails,
      gpsLatitude, gpsLongitude, gpsAccuracy, gpsTimestamp,
      weatherTemperature, weatherCondition, weatherDescription,
      weatherHumidity, weatherWindSpeed, weatherSuitability,
      collaborators, createdBy
    } = req.body;

    // Create DPR object
    const dprData = {
      date: new Date(date),
      sNo: parseInt(sNo),
      contractorName,
      agencyName,
      location,
      workDescription,
      node,
      pipeDiameter,
      units: units || 'RMT',
      engineerSupervisor,
      remarks,
      
      // Work Details
      workDetails: {
        hdpeNetwork: {
          excavation: parseFloat(hdpeExcavation) || 0,
          pipeLaying: parseFloat(hdpePipeLaying) || 0,
          backFilling: parseFloat(hdpeBackFilling) || 0
        },
        msNetwork: {
          excavation: parseFloat(msExcavation) || 0,
          pipeLaying: parseFloat(msPipeLaying) || 0,
          backFilling: parseFloat(msBackFilling) || 0
        },
        labour: {
          skilled: parseInt(skilledLabour) || 0,
          unskilled: parseInt(unskilledLabour) || 0,
          total: (parseInt(skilledLabour) || 0) + (parseInt(unskilledLabour) || 0)
        },
        targetQty: parseFloat(targetQty) || 0,
        lastSeasonQty: parseFloat(lastSeasonQty) || 0,
        thisSeasonQty: parseFloat(thisSeasonQty) || 0,
        cumulativeQty: parseFloat(cumulativeQty) || 0,
        balanceQty: parseFloat(balanceQty) || 0,
        concreteQty: parseFloat(concreteQty) || 0,
        machineryDetails
      },

      // GPS Location
      gpsLocation: gpsLatitude && gpsLongitude ? {
        latitude: parseFloat(gpsLatitude),
        longitude: parseFloat(gpsLongitude),
        accuracy: parseFloat(gpsAccuracy) || 0,
        timestamp: new Date(gpsTimestamp || Date.now()),
        location: {
          type: 'Point',
          coordinates: [parseFloat(gpsLongitude), parseFloat(gpsLatitude)]
        }
      } : undefined,

      // Weather Data
      weatherData: weatherTemperature ? {
        temperature: parseFloat(weatherTemperature),
        condition: weatherCondition,
        description: weatherDescription,
        humidity: parseFloat(weatherHumidity) || 0,
        windSpeed: parseFloat(weatherWindSpeed) || 0,
        workSuitability: weatherSuitability || 'Good'
      } : undefined,

      // Collaborators
      collaborators: collaborators ? JSON.parse(collaborators) : [],

      // Audit
      createdBy: createdBy || req.user?.id,
      status: 'active'
    };

    // Process file attachments
    if (req.files && req.files.length > 0) {
      dprData.attachments = req.files.map((file, index) => {
        // Get photo type from form data or filename
        const photoTypeField = `photoType_${index}`;
        const photoType = req.body[photoTypeField] || 'general';

        return {
          name: `${photoType}-${Date.now()}-${index}`,
          originalName: file.originalname,
          mimeType: file.mimetype,
          size: file.size,
          photoType: photoType,
          data: file.buffer, // Store as Buffer in MongoDB
          uploadedBy: createdBy || req.user?.id || 'system'
        };
      });
    }

    // Create and save DPR
    const dpr = new DPR(dprData);
    await dpr.save();

    // Return response without binary data
    const responseData = await DPR.findById(dpr._id)
      .select('-attachments.data')
      .populate('createdBy', 'name email');

    res.status(201).json({
      success: true,
      message: 'DPR report created successfully',
      data: responseData
    });

  } catch (error) {
    console.error('Error creating DPR report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create DPR report',
      error: error.message
    });
  }
});

// 🔄 PUT /api/dpr/:id - Update DPR report
router.put('/:id', upload.array('newAttachments', 10), async (req, res) => {
  try {
    const report = await DPR.findById(req.params.id);
    if (!report) {
      return res.status(404).json({
        success: false,
        message: 'DPR report not found'
      });
    }

    // Update fields from request body
    Object.keys(req.body).forEach(key => {
      if (key !== 'newAttachments' && req.body[key] !== undefined) {
        report[key] = req.body[key];
      }
    });

    // Add new attachments if provided
    if (req.files && req.files.length > 0) {
      const newAttachments = req.files.map((file, index) => ({
        name: `updated-${Date.now()}-${index}`,
        originalName: file.originalname,
        mimeType: file.mimetype,
        size: file.size,
        photoType: req.body[`photoType_${index}`] || 'general',
        data: file.buffer,
        uploadedBy: req.user?.id || 'system'
      }));
      
      report.attachments.push(...newAttachments);
    }

    report.updatedBy = req.user?.id;
    await report.save();

    const responseData = await DPR.findById(report._id)
      .select('-attachments.data')
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email');

    res.json({
      success: true,
      message: 'DPR report updated successfully',
      data: responseData
    });

  } catch (error) {
    console.error('Error updating DPR report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update DPR report',
      error: error.message
    });
  }
});

// 🗑️ DELETE /api/dpr/:id - Delete DPR report
router.delete('/:id', async (req, res) => {
  try {
    const report = await DPR.findByIdAndDelete(req.params.id);
    if (!report) {
      return res.status(404).json({
        success: false,
        message: 'DPR report not found'
      });
    }

    res.json({
      success: true,
      message: 'DPR report deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting DPR report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete DPR report',
      error: error.message
    });
  }
});

// 🗑️ DELETE /api/dpr/:id/attachment/:attachmentId - Delete specific attachment
router.delete('/:id/attachment/:attachmentId', async (req, res) => {
  try {
    const report = await DPR.findById(req.params.id);
    if (!report) {
      return res.status(404).json({
        success: false,
        message: 'DPR report not found'
      });
    }

    const attachment = report.attachments.id(req.params.attachmentId);
    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    attachment.remove();
    report.updatedBy = req.user?.id;
    await report.save();

    res.json({
      success: true,
      message: 'Attachment deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting attachment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete attachment',
      error: error.message
    });
  }
});

module.exports = router;
