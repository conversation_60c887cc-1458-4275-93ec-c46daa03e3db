const mongoose = require('mongoose');

// 📸 Attachment Schema for storing photos and files
const AttachmentSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  originalName: {
    type: String,
    required: true
  },
  mimeType: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    required: true
  },
  photoType: {
    type: String,
    enum: ['general', 'before-work', 'progress', 'completion', 'machinery', 'safety'],
    default: 'general'
  },
  // Store file as binary data (Buffer) in MongoDB
  data: {
    type: Buffer,
    required: true
  },
  uploadedAt: {
    type: Date,
    default: Date.now
  },
  uploadedBy: {
    type: String,
    required: true
  }
});

// 🌍 GPS Location Schema with geospatial support
const GPSLocationSchema = new mongoose.Schema({
  // GeoJSON Point for MongoDB geospatial queries
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      required: true,
      index: '2dsphere' // Enable geospatial indexing
    }
  },
  // Individual coordinates for easy access
  latitude: {
    type: Number,
    required: true,
    min: -90,
    max: 90
  },
  longitude: {
    type: Number,
    required: true,
    min: -180,
    max: 180
  },
  accuracy: {
    type: Number, // in meters
    required: true
  },
  altitude: {
    type: Number, // in meters above sea level
    default: null
  },
  heading: {
    type: Number, // compass direction (0-360 degrees)
    default: null
  },
  speed: {
    type: Number, // in m/s
    default: null
  },
  timestamp: {
    type: Date,
    required: true
  },
  // Reverse geocoded address (can be populated later)
  address: {
    street: String,
    city: String,
    state: String,
    country: String,
    postalCode: String,
    formattedAddress: String
  },
  // Site boundary verification
  withinSiteBoundary: {
    type: Boolean,
    default: false
  },
  siteName: String
});

// 👥 Collaborator Schema
const CollaboratorSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  name: {
    type: String,
    required: true
  },
  role: {
    type: String,
    required: true
  },
  email: String,
  phone: String,
  online: {
    type: Boolean,
    default: false
  },
  joinedAt: {
    type: Date,
    default: Date.now
  }
});

// 🏗️ Work Details Schema
const WorkDetailsSchema = new mongoose.Schema({
  // HDPE Network
  hdpeNetwork: {
    excavation: { type: Number, default: 0 },
    pipeLaying: { type: Number, default: 0 },
    backFilling: { type: Number, default: 0 }
  },
  // MS Network
  msNetwork: {
    excavation: { type: Number, default: 0 },
    pipeLaying: { type: Number, default: 0 },
    backFilling: { type: Number, default: 0 }
  },
  // Labour Details
  labour: {
    skilled: { type: Number, default: 0 },
    unskilled: { type: Number, default: 0 },
    total: { type: Number, default: 0 }
  },
  // Quantities
  targetQty: { type: Number, default: 0 },
  lastSeasonQty: { type: Number, default: 0 },
  thisSeasonQty: { type: Number, default: 0 },
  cumulativeQty: { type: Number, default: 0 },
  balanceQty: { type: Number, default: 0 },
  concreteQty: { type: Number, default: 0 },
  
  // Machinery and Equipment
  machineryDetails: String,
  equipmentUsed: [{
    name: String,
    type: String,
    hours: Number,
    fuelConsumption: Number
  }]
});

// 🌤️ Weather Data Schema
const WeatherDataSchema = new mongoose.Schema({
  temperature: Number,
  condition: String,
  description: String,
  humidity: Number,
  windSpeed: Number,
  workSuitability: {
    type: String,
    enum: ['Excellent', 'Good', 'Moderate', 'Poor', 'Dangerous'],
    default: 'Good'
  },
  recordedAt: {
    type: Date,
    default: Date.now
  }
});

// 📋 Main DPR Schema
const DPRSchema = new mongoose.Schema({
  // Basic Information
  date: {
    type: Date,
    required: true,
    index: true
  },
  sNo: {
    type: Number,
    required: true
  },
  
  // Project Details
  contractorName: {
    type: String,
    required: true,
    index: true
  },
  agencyName: {
    type: String,
    required: true
  },
  location: {
    type: String,
    required: true,
    index: true
  },
  workDescription: {
    type: String,
    required: true
  },
  
  // Technical Details
  node: String,
  pipeDiameter: String,
  units: {
    type: String,
    default: 'RMT'
  },
  
  // Personnel
  engineerSupervisor: String,
  collaborators: [CollaboratorSchema],
  
  // Work Progress
  workDetails: WorkDetailsSchema,
  
  // 📸 ATTACHMENTS (Photos and Files stored as binary data)
  attachments: [AttachmentSchema],
  
  // 🌍 GPS LOCATION (with geospatial support)
  gpsLocation: GPSLocationSchema,
  
  // 🌤️ Weather Information
  weatherData: WeatherDataSchema,
  
  // Status and Metadata
  status: {
    type: String,
    enum: ['draft', 'active', 'completed', 'cancelled'],
    default: 'active',
    index: true
  },
  remarks: String,
  
  // Audit Trail
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  
  // Version Control
  version: {
    type: Number,
    default: 1
  },
  
  // Approval Workflow
  approvalStatus: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'revision_required'],
    default: 'pending'
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approvedAt: Date,
  approvalComments: String
}, {
  timestamps: true, // Automatically adds createdAt and updatedAt
  collection: 'dpr_reports' // Explicit collection name
});

// 📊 Indexes for Performance
DPRSchema.index({ date: -1, location: 1 });
DPRSchema.index({ contractorName: 1, date: -1 });
DPRSchema.index({ 'gpsLocation.location': '2dsphere' }); // Geospatial index
DPRSchema.index({ status: 1, createdAt: -1 });

// 🔍 Text Search Index
DPRSchema.index({
  workDescription: 'text',
  location: 'text',
  contractorName: 'text',
  remarks: 'text'
});

// 📈 Virtual Fields
DPRSchema.virtual('totalAttachments').get(function() {
  return this.attachments ? this.attachments.length : 0;
});

DPRSchema.virtual('totalAttachmentSize').get(function() {
  if (!this.attachments) return 0;
  return this.attachments.reduce((total, attachment) => total + attachment.size, 0);
});

// 🔧 Methods
DPRSchema.methods.addAttachment = function(attachmentData) {
  this.attachments.push(attachmentData);
  return this.save();
};

DPRSchema.methods.removeAttachment = function(attachmentId) {
  this.attachments.id(attachmentId).remove();
  return this.save();
};

DPRSchema.methods.updateGPSLocation = function(gpsData) {
  this.gpsLocation = {
    ...gpsData,
    location: {
      type: 'Point',
      coordinates: [gpsData.longitude, gpsData.latitude]
    }
  };
  return this.save();
};

// 📊 Static Methods
DPRSchema.statics.findByLocation = function(latitude, longitude, radiusInMeters = 1000) {
  return this.find({
    'gpsLocation.location': {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        $maxDistance: radiusInMeters
      }
    }
  });
};

DPRSchema.statics.findByDateRange = function(startDate, endDate) {
  return this.find({
    date: {
      $gte: startDate,
      $lte: endDate
    }
  }).sort({ date: -1 });
};

// 🔄 Pre-save Middleware
DPRSchema.pre('save', function(next) {
  // Update the updatedAt timestamp
  this.updatedAt = new Date();
  
  // Ensure GPS location coordinates are properly formatted
  if (this.gpsLocation && this.gpsLocation.latitude && this.gpsLocation.longitude) {
    this.gpsLocation.location = {
      type: 'Point',
      coordinates: [this.gpsLocation.longitude, this.gpsLocation.latitude]
    };
  }
  
  // Calculate total labour if individual counts are provided
  if (this.workDetails && this.workDetails.labour) {
    const labour = this.workDetails.labour;
    if (labour.skilled !== undefined && labour.unskilled !== undefined) {
      labour.total = labour.skilled + labour.unskilled;
    }
  }
  
  next();
});

module.exports = mongoose.model('DPR', DPRSchema);
