const DPR = require('../models/DPR');

class DPRService {
  
  // 📋 Create new DPR with photos and GPS
  async createDPR(dprData, attachments = []) {
    try {
      // Process attachments to store as binary data
      const processedAttachments = attachments.map(attachment => ({
        name: attachment.name,
        originalName: attachment.originalName || attachment.name,
        mimeType: attachment.mimeType || attachment.type,
        size: attachment.size,
        photoType: attachment.photoType || 'general',
        data: Buffer.from(attachment.data, 'base64'), // Convert base64 to Buffer
        uploadedBy: dprData.createdBy || 'system',
        uploadedAt: new Date()
      }));

      // Create DPR with processed data
      const dpr = new DPR({
        ...dprData,
        attachments: processedAttachments
      });

      await dpr.save();
      
      // Return without binary data for response
      return await DPR.findById(dpr._id)
        .select('-attachments.data')
        .populate('createdBy', 'name email');
        
    } catch (error) {
      console.error('Error creating DPR:', error);
      throw new Error(`Failed to create DPR: ${error.message}`);
    }
  }

  // 📋 Get DPR reports with filtering
  async getDPRReports(filters = {}, options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        includeAttachments = false
      } = options;

      const skip = (page - 1) * limit;
      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

      // Build MongoDB filter
      const mongoFilter = {};
      if (filters.date) mongoFilter.date = new Date(filters.date);
      if (filters.dateRange) {
        mongoFilter.date = {
          $gte: new Date(filters.dateRange.start),
          $lte: new Date(filters.dateRange.end)
        };
      }
      if (filters.location) mongoFilter.location = new RegExp(filters.location, 'i');
      if (filters.contractor) mongoFilter.contractorName = new RegExp(filters.contractor, 'i');
      if (filters.status) mongoFilter.status = filters.status;

      // Select fields (exclude binary data unless specifically requested)
      const selectFields = includeAttachments ? '' : '-attachments.data';

      const reports = await DPR.find(mongoFilter)
        .select(selectFields)
        .populate('createdBy', 'name email')
        .populate('approvedBy', 'name email')
        .sort(sort)
        .skip(skip)
        .limit(limit);

      const total = await DPR.countDocuments(mongoFilter);

      return {
        data: reports,
        pagination: {
          current: page,
          pages: Math.ceil(total / limit),
          total,
          limit
        }
      };
    } catch (error) {
      console.error('Error fetching DPR reports:', error);
      throw new Error(`Failed to fetch DPR reports: ${error.message}`);
    }
  }

  // 📋 Get single DPR by ID
  async getDPRById(id, includeAttachments = true) {
    try {
      const selectFields = includeAttachments ? '' : '-attachments.data';
      
      const report = await DPR.findById(id)
        .select(selectFields)
        .populate('createdBy', 'name email')
        .populate('approvedBy', 'name email');

      if (!report) {
        throw new Error('DPR report not found');
      }

      return report;
    } catch (error) {
      console.error('Error fetching DPR by ID:', error);
      throw new Error(`Failed to fetch DPR: ${error.message}`);
    }
  }

  // 📸 Get attachment by ID
  async getAttachment(dprId, attachmentId) {
    try {
      const report = await DPR.findById(dprId);
      if (!report) {
        throw new Error('DPR report not found');
      }

      const attachment = report.attachments.id(attachmentId);
      if (!attachment) {
        throw new Error('Attachment not found');
      }

      return attachment;
    } catch (error) {
      console.error('Error fetching attachment:', error);
      throw new Error(`Failed to fetch attachment: ${error.message}`);
    }
  }

  // 🔄 Update DPR
  async updateDPR(id, updateData, newAttachments = []) {
    try {
      const report = await DPR.findById(id);
      if (!report) {
        throw new Error('DPR report not found');
      }

      // Update fields
      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined) {
          report[key] = updateData[key];
        }
      });

      // Add new attachments
      if (newAttachments.length > 0) {
        const processedAttachments = newAttachments.map(attachment => ({
          name: attachment.name,
          originalName: attachment.originalName || attachment.name,
          mimeType: attachment.mimeType || attachment.type,
          size: attachment.size,
          photoType: attachment.photoType || 'general',
          data: Buffer.from(attachment.data, 'base64'),
          uploadedBy: updateData.updatedBy || 'system',
          uploadedAt: new Date()
        }));

        report.attachments.push(...processedAttachments);
      }

      await report.save();

      return await DPR.findById(id)
        .select('-attachments.data')
        .populate('createdBy', 'name email')
        .populate('updatedBy', 'name email');
        
    } catch (error) {
      console.error('Error updating DPR:', error);
      throw new Error(`Failed to update DPR: ${error.message}`);
    }
  }

  // 🗑️ Delete DPR
  async deleteDPR(id) {
    try {
      const report = await DPR.findByIdAndDelete(id);
      if (!report) {
        throw new Error('DPR report not found');
      }
      return { message: 'DPR report deleted successfully' };
    } catch (error) {
      console.error('Error deleting DPR:', error);
      throw new Error(`Failed to delete DPR: ${error.message}`);
    }
  }

  // 🗑️ Delete attachment
  async deleteAttachment(dprId, attachmentId) {
    try {
      const report = await DPR.findById(dprId);
      if (!report) {
        throw new Error('DPR report not found');
      }

      const attachment = report.attachments.id(attachmentId);
      if (!attachment) {
        throw new Error('Attachment not found');
      }

      attachment.remove();
      await report.save();

      return { message: 'Attachment deleted successfully' };
    } catch (error) {
      console.error('Error deleting attachment:', error);
      throw new Error(`Failed to delete attachment: ${error.message}`);
    }
  }

  // 🌍 Find DPRs by location (geospatial query)
  async findDPRsByLocation(latitude, longitude, radiusInMeters = 1000) {
    try {
      const reports = await DPR.find({
        'gpsLocation.location': {
          $near: {
            $geometry: {
              type: 'Point',
              coordinates: [longitude, latitude]
            },
            $maxDistance: radiusInMeters
          }
        }
      })
      .select('-attachments.data')
      .populate('createdBy', 'name email');

      return reports;
    } catch (error) {
      console.error('Error finding DPRs by location:', error);
      throw new Error(`Failed to find DPRs by location: ${error.message}`);
    }
  }

  // 📊 Get DPR statistics
  async getDPRStatistics(filters = {}) {
    try {
      const matchStage = {};
      if (filters.dateRange) {
        matchStage.date = {
          $gte: new Date(filters.dateRange.start),
          $lte: new Date(filters.dateRange.end)
        };
      }
      if (filters.location) matchStage.location = new RegExp(filters.location, 'i');
      if (filters.contractor) matchStage.contractorName = new RegExp(filters.contractor, 'i');

      const stats = await DPR.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: null,
            totalReports: { $sum: 1 },
            totalAttachments: { $sum: { $size: '$attachments' } },
            avgAttachmentsPerReport: { $avg: { $size: '$attachments' } },
            totalWorkDays: { $addToSet: '$date' },
            contractors: { $addToSet: '$contractorName' },
            locations: { $addToSet: '$location' },
            statusBreakdown: {
              $push: '$status'
            }
          }
        },
        {
          $project: {
            totalReports: 1,
            totalAttachments: 1,
            avgAttachmentsPerReport: { $round: ['$avgAttachmentsPerReport', 2] },
            uniqueWorkDays: { $size: '$totalWorkDays' },
            uniqueContractors: { $size: '$contractors' },
            uniqueLocations: { $size: '$locations' },
            statusBreakdown: 1
          }
        }
      ]);

      return stats[0] || {
        totalReports: 0,
        totalAttachments: 0,
        avgAttachmentsPerReport: 0,
        uniqueWorkDays: 0,
        uniqueContractors: 0,
        uniqueLocations: 0,
        statusBreakdown: []
      };
    } catch (error) {
      console.error('Error getting DPR statistics:', error);
      throw new Error(`Failed to get DPR statistics: ${error.message}`);
    }
  }

  // 🔍 Search DPRs with text search
  async searchDPRs(searchTerm, options = {}) {
    try {
      const { page = 1, limit = 10 } = options;
      const skip = (page - 1) * limit;

      const reports = await DPR.find(
        { $text: { $search: searchTerm } },
        { score: { $meta: 'textScore' } }
      )
      .select('-attachments.data')
      .populate('createdBy', 'name email')
      .sort({ score: { $meta: 'textScore' } })
      .skip(skip)
      .limit(limit);

      const total = await DPR.countDocuments({ $text: { $search: searchTerm } });

      return {
        data: reports,
        pagination: {
          current: page,
          pages: Math.ceil(total / limit),
          total,
          limit
        }
      };
    } catch (error) {
      console.error('Error searching DPRs:', error);
      throw new Error(`Failed to search DPRs: ${error.message}`);
    }
  }
}

module.exports = new DPRService();
