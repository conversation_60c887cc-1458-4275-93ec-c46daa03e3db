import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Card,
  CardContent,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  TextField,
  MenuItem,
  Divider,
  LinearProgress,
  Avatar,
  Badge,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tabs,
  Tab,
  Alert,
  AlertTitle,
  Snackbar,
  CircularProgress,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Autocomplete,
  Slider,
  Rating,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Switch,
  FormControlLabel,
  InputAdornment,
  Fab
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  Assessment as ReportIcon,
  Construction as ConstructionIcon,
  Timeline as TimelineIcon,
  TrendingUp as TrendingUpIcon,
  Group as GroupIcon,
  LocationOn as LocationIcon,
  Engineering as EngineeringIcon,
  CalendarToday as CalendarIcon,
  Dashboard as DashboardIcon,
  Analytics as AnalyticsIcon,
  CloudUpload as UploadIcon,
  Share as ShareIcon,
  Notifications as NotificationsIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  PhotoCamera as PhotoIcon,
  AttachFile as AttachFileIcon,
  Map as MapIcon,
  QrCode as QrCodeIcon,
  Backup as BackupIcon,
  Settings as SettingsIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Delete as DeleteIcon,
  GetApp as ExportIcon,
  WbSunny as SunnyIcon,
  Cloud as CloudyIcon,
  Opacity as RainIcon,
  AcUnit as SnowIcon,
  Air as WindIcon,
  WaterDrop as HumidityIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import PremiumDialog from '../common/PremiumDialog';
import { usePermissions } from '../../hooks/usePermissions';
import Cookies from 'js-cookie';
import axios from 'axios';

const DailyProgressReport = () => {
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);

  // Form state for new DPR entry
  const [formData, setFormData] = useState({
    // Basic Information
    date: new Date().toISOString().split('T')[0],
    sNo: '',
    contractorName: '',
    node: '',
    pipeDiameter: '',
    units: 'RMT',
    location: '',
    remarks: '',
    engineerSupervisor: '',

    // HDPE Network Line
    hdpeExcavation: 0,
    hdpePipeLaying: 0,
    hdpeBackFilling: 0,

    // MS Network Line
    msExcavation: 0,
    msPipeLaying: 0,
    msBackFilling: 0,

    // Work Description Details
    workDescription: '',
    agencyName: '',

    // Summary Section
    description: '',
    targetQty: 0,
    lastSeasonQty: 0,
    thisSeasonQty: 0,
    cumulativeQty: 0,
    balanceQty: 0,

    // Additional Details
    skilledLabour: 0,
    unskilledLabour: 0,
    totalLabour: 0,
    concreteQty: 0,
    machineryDetails: ''
  });
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [filterLocation, setFilterLocation] = useState('all');
  const [filterContractor, setFilterContractor] = useState('all');

  // 🚀 PREMIUM ADVANCED FEATURES STATE
  const [currentTab, setCurrentTab] = useState(0);
  const [viewMode, setViewMode] = useState('table'); // 'table', 'cards', 'timeline', 'analytics', 'kanban'
  const [selectedReports, setSelectedReports] = useState([]);
  const [bulkActions, setBulkActions] = useState(false);
  const [notifications, setNotifications] = useState([
    { id: 1, type: 'success', message: 'DPR entry saved successfully', time: '2 min ago' },
    { id: 3, type: 'info', message: 'New team member added to project', time: '1 hour ago' }
  ]);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [autoSave, setAutoSave] = useState(true);
  const [realTimeUpdates, setRealTimeUpdates] = useState(true);
  const [advancedFilters, setAdvancedFilters] = useState({
    dateRange: { start: null, end: null },
    contractor: '',
    location: '',
    status: 'all',
    priority: 'all',
    progress: [0, 100],
    workType: 'all',
    qualityRating: [0, 5]
  });
  const [exportOptions, setExportOptions] = useState({
    format: 'pdf',
    includeCharts: true,
    includePhotos: true,
    template: 'detailed',
    dateRange: 'today'
  });
  const [dashboardMetrics, setDashboardMetrics] = useState({
    totalReports: 0,
    completedToday: 0,
    avgProgress: 0,
    criticalIssues: 0,
    activeContractors: 0,
    totalLabour: 0,
    weatherImpact: 'low',
    safetyScore: 95,
    qualityScore: 4.2,
    budgetUtilization: 78
  });
  const [weatherLoading, setWeatherLoading] = useState(true);
  const [weatherError, setWeatherError] = useState(false);
  const [weatherData, setWeatherData] = useState({
    temperature: 'Loading...',
    condition: 'Loading...',
    humidity: 'Loading...',
    windSpeed: 'Loading...',
    forecast: 'Loading...',
    workSuitability: 'Loading...',
    suitabilityReason: 'Loading weather data...',
    icon: null,
    location: 'Loading...',
    forecastDays: [],
    // Enhanced weather data
    precipitation: 0,
    cloudCover: 0,
    uvIndex: 0,
    visibility: 0,
    pressure: 0,
    dewPoint: 0,
    hourlyForecast: [],
    weatherAlerts: [],
    sunrise: null,
    sunset: null,
    moonPhase: 0
  });
  const [qualityRating, setQualityRating] = useState(4.5);
  const [safetyScore, setSafetyScore] = useState(95);
  const [attachments, setAttachments] = useState([]);
  const [gpsLocation, setGpsLocation] = useState(null);
  const [voiceNotes, setVoiceNotes] = useState([]);
  const [collaborators, setCollaborators] = useState([
    { id: 1, name: 'John Doe', role: 'Site Engineer', avatar: 'JD', online: true },
    { id: 2, name: 'Jane Smith', role: 'Project Manager', avatar: 'JS', online: false },
    { id: 3, name: 'Mike Johnson', role: 'Quality Inspector', avatar: 'MJ', online: true }
  ]);
  const [templates, setTemplates] = useState([
    { id: 1, name: 'Standard Construction', category: 'Construction' },
    { id: 2, name: 'Highway Project', category: 'Construction' },
    { id: 3, name: 'Building Foundation', category: 'Construction' },
    { id: 4, name: 'Pipe Installation', category: 'Plumbing' },
    { id: 5, name: 'Electrical Work', category: 'Electrical' }
  ]);
  const [aiSuggestions, setAiSuggestions] = useState([
    'Consider increasing manpower for excavation work',
    'Weather conditions are optimal for concrete work',
    'Material delivery scheduled for tomorrow'
  ]);
  const [workflowStatus, setWorkflowStatus] = useState('draft'); // draft, review, approved, published
  const [openAdvancedFilters, setOpenAdvancedFilters] = useState(false);
  const [timeTracking, setTimeTracking] = useState({
    startTime: null,
    endTime: null,
    breakTime: 0,
    overtimeHours: 0
  });
  const [costTracking, setCostTracking] = useState({
    budgetAllocated: 100000,
    actualSpent: 78000,
    projectedCost: 95000,
    variance: -5000
  });
  const [riskAssessment, setRiskAssessment] = useState({
    weatherRisk: 'low',
    safetyRisk: 'medium',
    qualityRisk: 'low',
    scheduleRisk: 'high'
  });
  const [complianceChecks, setComplianceChecks] = useState({
    safetyCompliance: true,
    environmentalCompliance: true,
    qualityStandards: true,
    regulatoryApproval: false
  });

  // Get user role and permissions
  const userRole = Cookies.get('userRole');
  const { canView, canManage } = usePermissions();

  // For DPR, allow access to all roles except 'user'
  const canViewDPR = userRole !== 'user';
  const canManageDPR = userRole === 'admin' || userRole === 'superadmin' || userRole === 'storekeeper' || userRole === 'siteengineer';

  // Debug logging
  console.log('🔍 DPR Debug Info:', {
    userRole,
    canViewDPR,
    canManageDPR
  });

  // Form handlers
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Auto-calculate balance quantity
    if (['targetQty', 'cumulativeQty'].includes(field)) {
      const target = field === 'targetQty' ? value : formData.targetQty;
      const cumulative = field === 'cumulativeQty' ? value : formData.cumulativeQty;
      setFormData(prev => ({
        ...prev,
        balanceQty: target - cumulative
      }));
    }

    // Auto-calculate total labour
    if (['skilledLabour', 'unskilledLabour'].includes(field)) {
      const skilled = field === 'skilledLabour' ? value : formData.skilledLabour;
      const unskilled = field === 'unskilledLabour' ? value : formData.unskilledLabour;
      setFormData(prev => ({
        ...prev,
        totalLabour: skilled + unskilled
      }));
    }
  };

  const handleSubmit = () => {
    // Validate required fields
    const requiredFields = ['contractorName', 'location', 'workDescription', 'agencyName'];
    const missingFields = requiredFields.filter(field => !formData[field]);

    if (missingFields.length > 0) {
      alert(`Please fill in the following required fields: ${missingFields.join(', ')}`);
      return;
    }

    // Create new report entry
    const newReport = {
      id: Date.now(),
      date: formData.date,
      sNo: formData.sNo || reports.length + 1,
      contractorName: formData.contractorName,
      node: formData.node,
      pipeDiameter: formData.pipeDiameter,
      units: formData.units,
      location: formData.location,
      remarks: formData.remarks,
      engineerSupervisor: formData.engineerSupervisor,

      // Network data
      hdpeNetwork: {
        excavation: formData.hdpeExcavation,
        pipeLaying: formData.hdpePipeLaying,
        backFilling: formData.hdpeBackFilling
      },
      msNetwork: {
        excavation: formData.msExcavation,
        pipeLaying: formData.msPipeLaying,
        backFilling: formData.msBackFilling
      },

      // Work details
      workDescription: formData.workDescription,
      agencyName: formData.agencyName,

      // Summary
      targetQty: formData.targetQty,
      lastSeasonQty: formData.lastSeasonQty,
      thisSeasonQty: formData.thisSeasonQty,
      cumulativeQty: formData.cumulativeQty,
      balanceQty: formData.balanceQty,

      // Labour details
      labour: {
        skilled: formData.skilledLabour,
        unskilled: formData.unskilledLabour,
        total: formData.totalLabour
      },

      concreteQty: formData.concreteQty,
      machineryDetails: formData.machineryDetails,

      status: 'active',
      createdAt: new Date().toISOString()
    };

    // Add to reports list
    setReports(prev => [...prev, newReport]);

    // Reset form and close dialog
    setFormData({
      date: new Date().toISOString().split('T')[0],
      sNo: '',
      contractorName: '',
      node: '',
      pipeDiameter: '',
      units: 'RMT',
      location: '',
      remarks: '',
      engineerSupervisor: '',
      hdpeExcavation: 0,
      hdpePipeLaying: 0,
      hdpeBackFilling: 0,
      msExcavation: 0,
      msPipeLaying: 0,
      msBackFilling: 0,
      workDescription: '',
      agencyName: '',
      description: '',
      targetQty: 0,
      lastSeasonQty: 0,
      thisSeasonQty: 0,
      cumulativeQty: 0,
      balanceQty: 0,
      skilledLabour: 0,
      unskilledLabour: 0,
      totalLabour: 0,
      concreteQty: 0,
      machineryDetails: ''
    });

    setOpenDialog(false);
    alert('DPR entry added successfully!');
  };

  // Enhanced sample data structure based on both Excel formats
  const [sampleReports] = useState([
    {
      id: 1,
      date: '2025-01-06',
      location: 'Pandhurna Dam Balancing Reservoir',
      workDescription: 'MS & HDPE Pipe Network Line Excavation & Laying work in Progress.',
      agency: 'MANTENA INFRASOL PVT LTD',
      workDetails: {
        excavation: { today: 50, cumulative: 354, target: 400 },
        pipeLaying: { today: 45, cumulative: 354, target: 400 },
        backFilling: { today: 40, cumulative: 300, target: 400 }
      },
      manpower: {
        skilled: 5,
        unskilled: 8,
        total: 13
      },
      machinery: {
        excavator: 1,
        dumper: 2,
        rocMachine: 0
      },
      materials: {
        cement: { consumed: 0, unit: 'bags' },
        steel: { consumed: 0, unit: 'tons' },
        pipes: { consumed: 25, unit: 'meters' }
      },
      status: 'active',
      remarks: 'Work progressing as per schedule'
    },
    {
      id: 2,
      date: '2025-01-06',
      location: 'Pump House -8 Intake Well (Gorakhpur)',
      workDescription: 'ECB Column Shuttering & Pump house backfilling work. Today RCC Qty: 0 Cum @M35. Today Plump Concrete Qty: 0 Cum @ Uptodate Concrete Qty: 785.4 cum',
      agency: 'MANTENA INFRASOL PVT LTD',
      workDetails: {
        rccWork: { today: 0, cumulative: 785.4, target: 1000 },
        shuttering: { today: 15, cumulative: 450, target: 600 },
        backFilling: { today: 20, cumulative: 380, target: 500 }
      },
      manpower: {
        skilled: 8,
        unskilled: 12,
        total: 20
      },
      machinery: {
        excavator: 1,
        dumper: 1,
        rocMachine: 0
      },
      materials: {
        cement: { consumed: 50, unit: 'bags' },
        steel: { consumed: 2.5, unit: 'tons' },
        aggregate: { consumed: 15, unit: 'cum' }
      },
      status: 'active',
      remarks: 'Concrete work completed, shuttering in progress'
    },
    {
      id: 3,
      date: '2025-01-06',
      location: 'WRD Camp Office (Linga)',
      workDescription: 'Slab Parafit wall brick masonry work & Brick shifting from ground floor to slab work in progress. Today Concrete Qty: 0 Cum @ M25. Uptodate Concrete Qty: 181.936 Cum',
      agency: 'Danish Parvez',
      workDetails: {
        brickWork: { today: 25, cumulative: 181.936, target: 250 },
        concreteWork: { today: 0, cumulative: 181.936, target: 200 },
        shifting: { today: 100, cumulative: 500, target: 800 }
      },
      manpower: {
        skilled: 2,
        unskilled: 3,
        total: 5
      },
      machinery: {
        excavator: 0,
        dumper: 0,
        rocMachine: 0
      },
      materials: {
        bricks: { consumed: 1000, unit: 'nos' },
        cement: { consumed: 25, unit: 'bags' },
        sand: { consumed: 5, unit: 'cum' }
      },
      status: 'active',
      remarks: 'Masonry work in progress'
    },
    {
      id: 4,
      date: '2025-01-06',
      location: 'Pump House -6 (Saroth)',
      workDescription: 'No Work. Today Qty: 0 Cum. Soft/Black Soil: 0 Cum. Hard Soil (Murrum): 0 Cum. Hard Rock: 0 Cum. Up to Date Quantity: 11819 Cum. Drilling Holes: 0 Nos (12 Ft) Blasting Holes: 0 Nos',
      agency: 'GV Infra Projects',
      workDetails: {
        excavation: { today: 0, cumulative: 11819, target: 12000 },
        drilling: { today: 0, cumulative: 150, target: 200 },
        blasting: { today: 0, cumulative: 80, target: 100 }
      },
      manpower: {
        skilled: 0,
        unskilled: 0,
        total: 0
      },
      machinery: {
        excavator: 0,
        dumper: 0,
        rocMachine: 0
      },
      materials: {
        explosives: { consumed: 0, unit: 'kg' },
        diesel: { consumed: 0, unit: 'liters' }
      },
      status: 'idle',
      remarks: 'No work today'
    }
  ]);

  useEffect(() => {
    setReports(sampleReports);
  }, [sampleReports]);

  // Check if user has permission to view DPR
  if (!canViewDPR) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h5" color="error" sx={{ mb: 2 }}>
          Access Denied
        </Typography>
        <Typography variant="body1" sx={{ color: '#64748b' }}>
          You don't have permission to view Daily Progress Reports.
        </Typography>
      </Box>
    );
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'active': return 'warning';
      case 'pending': return 'error';
      default: return 'default';
    }
  };

  const calculateProgress = (completed, target) => {
    return target > 0 ? Math.round((completed / target) * 100) : 0;
  };

  const getProgressColor = (progress) => {
    if (progress >= 90) return 'success';
    if (progress >= 70) return 'warning';
    return 'error';
  };

  const filteredReports = reports.filter(report => {
    const dateMatch = report.date === selectedDate;
    const locationMatch = filterLocation === 'all' || report.location.toLowerCase().includes(filterLocation.toLowerCase());
    const contractorMatch = filterContractor === 'all' || report.agency.toLowerCase().includes(filterContractor.toLowerCase());
    return dateMatch && locationMatch && contractorMatch;
  });

  const uniqueLocations = [...new Set(reports.map(r => r.location))];
  const uniqueContractors = [...new Set(reports.map(r => r.agency))];

  const totalStats = filteredReports.reduce((acc, report) => {
    // Calculate totals from different work types
    Object.keys(report.workDetails).forEach(workType => {
      const work = report.workDetails[workType];
      if (work.today !== undefined) acc.totalTodayWork += work.today;
      if (work.cumulative !== undefined) acc.totalCumulative += work.cumulative;
      if (work.target !== undefined) acc.totalTarget += work.target;
    });

    // Manpower statistics
    acc.totalManpower += report.manpower.total;
    acc.totalSkilled += report.manpower.skilled;
    acc.totalUnskilled += report.manpower.unskilled;

    // Active sites count
    if (report.status === 'active') acc.activeSites++;
    if (report.status === 'idle') acc.idleSites++;

    return acc;
  }, {
    totalTodayWork: 0,
    totalCumulative: 0,
    totalTarget: 0,
    totalManpower: 0,
    totalSkilled: 0,
    totalUnskilled: 0,
    activeSites: 0,
    idleSites: 0
  });

  // Get weather data from Open-Meteo API
  const fetchWeatherData = useCallback(async () => {
    try {
      setWeatherLoading(true);
      setWeatherError(false);
      console.log('Fetching enhanced weather data from Open-Meteo...');

      // Hyderabad, India coordinates
      const lat = 17.4486;
      const lon = 78.3908;

      // Enhanced Open-Meteo API call with more parameters
      const currentParams = [
        'temperature_2m', 'relative_humidity_2m', 'apparent_temperature',
        'is_day', 'precipitation', 'rain', 'showers', 'snowfall',
        'weather_code', 'cloud_cover', 'pressure_msl', 'surface_pressure',
        'wind_speed_10m', 'wind_direction_10m', 'wind_gusts_10m'
      ].join(',');

      const hourlyParams = [
        'temperature_2m', 'relative_humidity_2m', 'apparent_temperature',
        'precipitation_probability', 'precipitation', 'rain', 'showers',
        'snowfall', 'snow_depth', 'weather_code', 'pressure_msl',
        'surface_pressure', 'cloud_cover', 'cloud_cover_low',
        'cloud_cover_mid', 'cloud_cover_high', 'visibility',
        'evapotranspiration', 'et0_fao_evapotranspiration',
        'vapour_pressure_deficit', 'wind_speed_10m', 'wind_direction_10m',
        'wind_gusts_10m', 'temperature_80m', 'temperature_120m',
        'temperature_180m', 'soil_temperature_0cm', 'soil_temperature_6cm',
        'soil_moisture_0_1cm', 'soil_moisture_1_3cm', 'uv_index'
      ].join(',');

      const dailyParams = [
        'weather_code', 'temperature_2m_max', 'temperature_2m_min',
        'apparent_temperature_max', 'apparent_temperature_min',
        'sunrise', 'sunset', 'daylight_duration', 'sunshine_duration',
        'uv_index_max', 'uv_index_clear_sky_max', 'precipitation_sum',
        'rain_sum', 'showers_sum', 'snowfall_sum', 'precipitation_hours',
        'precipitation_probability_max', 'wind_speed_10m_max',
        'wind_gusts_10m_max', 'wind_direction_10m_dominant',
        'shortwave_radiation_sum', 'et0_fao_evapotranspiration'
      ].join(',');

      const weatherApiUrl = `https://api.open-meteo.com/v1/forecast?latitude=${lat}&longitude=${lon}&current=${currentParams}&hourly=${hourlyParams}&daily=${dailyParams}&timezone=Asia%2FKolkata&forecast_days=7`;

      console.log(`Fetching from enhanced API: ${weatherApiUrl}`);

      const response = await axios.get(weatherApiUrl);
      const data = response.data;
      
      console.log('Enhanced weather data received:', data);
      console.log('Current temperature from API:', data.current?.temperature_2m);
      console.log('Current weather code:', data.current?.weather_code);

      // Extract current weather data
      const current = data.current;
      const hourly = data.hourly;
      const daily = data.daily;

      // Get current temperature and conditions
      const currentTemp = Math.round(current.temperature_2m || 30);
      const currentWindSpeed = Math.round(current.wind_speed_10m || 3);
      const currentHumidity = Math.round(current.relative_humidity_2m || 65);
      const currentPrecipitation = current.precipitation || 0;
      const currentCloudCover = current.cloud_cover || 0;
      const currentPressure = Math.round(current.pressure_msl || 1013);
      const weatherCode = current.weather_code || 0;

      console.log('Processed temperature:', currentTemp);
      console.log('Processed wind speed:', currentWindSpeed);
      console.log('Weather code:', weatherCode);

      // Enhanced weather condition logic based on WMO weather codes
      const getWeatherCondition = (code, isDay = true) => {
        const dayNight = isDay ? 'd' : 'n';

        if (code === 0) return { condition: 'Clear', description: 'clear sky', icon: `01${dayNight}` };
        if (code <= 3) return { condition: 'Partly Cloudy', description: 'partly cloudy', icon: `02${dayNight}` };
        if (code <= 48) return { condition: 'Foggy', description: 'fog', icon: `50${dayNight}` };
        if (code <= 57) return { condition: 'Drizzle', description: 'light drizzle', icon: `09${dayNight}` };
        if (code <= 67) return { condition: 'Rain', description: 'rain', icon: `10${dayNight}` };
        if (code <= 77) return { condition: 'Snow', description: 'snow', icon: `13${dayNight}` };
        if (code <= 82) return { condition: 'Showers', description: 'rain showers', icon: `09${dayNight}` };
        if (code <= 86) return { condition: 'Snow Showers', description: 'snow showers', icon: `13${dayNight}` };
        if (code <= 99) return { condition: 'Thunderstorm', description: 'thunderstorm', icon: `11${dayNight}` };

        return { condition: 'Clear', description: 'clear sky', icon: `01${dayNight}` };
      };

      const isDay = current.is_day === 1;
      const weatherCondition = getWeatherCondition(weatherCode, isDay);
      const { condition, description, icon } = weatherCondition;
      
      // Process hourly data for today's detailed forecast (next 24 hours)
      const hourlyForecast = [];
      const now = new Date();
      const currentHour = now.getHours();

      if (hourly && hourly.time && Array.isArray(hourly.time)) {
        // Get next 24 hours of data
        for (let i = 0; i < Math.min(24, hourly.time.length); i++) {
          const time = new Date(hourly.time[i]);
          const temp = Math.round(hourly.temperature_2m[i] || 30);
          const humidity = Math.round(hourly.relative_humidity_2m[i] || 65);
          const precipitation = hourly.precipitation[i] || 0;
          const precipitationProb = hourly.precipitation_probability[i] || 0;
          const windSpeed = Math.round(hourly.wind_speed_10m[i] || 3);
          const cloudCover = hourly.cloud_cover[i] || 0;
          const uvIndex = hourly.uv_index[i] || 0;
          const visibility = hourly.visibility[i] || 10000;
          const hourWeatherCode = hourly.weather_code[i] || 0;

          const hourWeatherCondition = getWeatherCondition(hourWeatherCode, time.getHours() >= 6 && time.getHours() <= 18);

          hourlyForecast.push({
            time: time.toISOString(),
            hour: time.getHours(),
            temp,
            humidity,
            precipitation,
            precipitationProb,
            windSpeed,
            cloudCover,
            uvIndex,
            visibility: Math.round(visibility / 1000), // Convert to km
            condition: hourWeatherCondition.condition,
            description: hourWeatherCondition.description,
            icon: hourWeatherCondition.icon,
            isNow: i === 0
          });
        }
      }

      // Process daily forecasts (next 7 days)
      const dailyForecasts = [];

      if (daily && daily.time && Array.isArray(daily.time)) {
        for (let i = 0; i < Math.min(7, daily.time.length); i++) {
          const date = daily.time[i];
          const maxTemp = Math.round(daily.temperature_2m_max[i] || 30);
          const minTemp = Math.round(daily.temperature_2m_min[i] || 20);
          const precipitationSum = daily.precipitation_sum[i] || 0;
          const precipitationProb = daily.precipitation_probability_max[i] || 0;
          const uvIndexMax = daily.uv_index_max[i] || 0;
          const windSpeedMax = Math.round(daily.wind_speed_10m_max[i] || 3);
          const dailyWeatherCode = daily.weather_code[i] || 0;
          const sunrise = daily.sunrise[i];
          const sunset = daily.sunset[i];

          const dailyWeatherCondition = getWeatherCondition(dailyWeatherCode, true);

          dailyForecasts.push({
            date,
            maxTemp,
            minTemp,
            temp: maxTemp, // For backward compatibility
            precipitationSum,
            precipitationProb,
            uvIndexMax,
            windSpeedMax,
            condition: dailyWeatherCondition.condition,
            description: dailyWeatherCondition.description,
            icon: dailyWeatherCondition.icon,
            sunrise,
            sunset
          });
        }
      }
      
      // Enhanced work suitability analysis
      let workSuitability = 'Excellent';
      let suitabilityReason = 'Optimal conditions for all construction activities';
      const weatherAlerts = [];

      // Temperature-based analysis
      if (currentTemp > 45) {
        workSuitability = 'Dangerous';
        suitabilityReason = 'Extreme heat - work should be suspended';
        weatherAlerts.push({
          type: 'error',
          title: 'Extreme Heat Warning',
          message: 'Temperature exceeds safe working limits. Consider suspending outdoor work.',
          icon: '🔥'
        });
      } else if (currentTemp > 40) {
        workSuitability = 'Poor';
        suitabilityReason = 'Extreme heat - high risk of heat stress, frequent breaks required';
        weatherAlerts.push({
          type: 'warning',
          title: 'Heat Advisory',
          message: 'High temperature detected. Ensure adequate hydration and frequent breaks.',
          icon: '🌡️'
        });
      } else if (currentTemp > 35) {
        workSuitability = 'Moderate';
        suitabilityReason = 'High temperature - implement heat safety measures';
        weatherAlerts.push({
          type: 'info',
          title: 'Hot Weather',
          message: 'Monitor workers for heat-related symptoms.',
          icon: '☀️'
        });
      } else if (currentTemp < 0) {
        workSuitability = 'Poor';
        suitabilityReason = 'Freezing conditions - equipment and material issues';
        weatherAlerts.push({
          type: 'warning',
          title: 'Freezing Alert',
          message: 'Sub-zero temperatures may affect concrete curing and equipment.',
          icon: '🧊'
        });
      } else if (currentTemp < 5) {
        workSuitability = 'Moderate';
        suitabilityReason = 'Very cold - slower work pace and equipment warm-up needed';
      } else if (currentTemp >= 20 && currentTemp <= 30) {
        workSuitability = 'Excellent';
        suitabilityReason = 'Optimal temperature range for maximum productivity';
      } else if (currentTemp >= 15 && currentTemp <= 35) {
        workSuitability = 'Good';
        suitabilityReason = currentTemp > 30 ? 'Warm but manageable conditions' : 'Cool but comfortable working conditions';
      }

      // Precipitation-based analysis
      if (currentPrecipitation > 10) {
        if (workSuitability === 'Excellent') workSuitability = 'Poor';
        else if (workSuitability === 'Good') workSuitability = 'Poor';
        suitabilityReason += ' • Heavy rain affects outdoor work';
        weatherAlerts.push({
          type: 'warning',
          title: 'Heavy Rain Alert',
          message: 'Heavy precipitation may halt outdoor construction activities.',
          icon: '🌧️'
        });
      } else if (currentPrecipitation > 2) {
        if (workSuitability === 'Excellent') workSuitability = 'Good';
        suitabilityReason += ' • Light rain may slow some activities';
        weatherAlerts.push({
          type: 'info',
          title: 'Light Rain',
          message: 'Light precipitation detected. Monitor conditions.',
          icon: '🌦️'
        });
      }

      // Wind-based analysis
      if (currentWindSpeed > 50) {
        workSuitability = 'Dangerous';
        suitabilityReason += ' • Dangerous wind speeds - suspend crane operations';
        weatherAlerts.push({
          type: 'error',
          title: 'High Wind Warning',
          message: 'Dangerous wind speeds. Suspend crane and high-altitude work.',
          icon: '💨'
        });
      } else if (currentWindSpeed > 30) {
        if (workSuitability === 'Excellent') workSuitability = 'Moderate';
        suitabilityReason += ' • High winds affect crane and scaffolding work';
        weatherAlerts.push({
          type: 'warning',
          title: 'Wind Advisory',
          message: 'High winds may affect crane operations and scaffolding.',
          icon: '🌬️'
        });
      }

      // Cloud cover and visibility
      if (currentCloudCover > 90 && hourlyForecast.length > 0) {
        const nextHourRain = hourlyForecast.slice(0, 3).some(h => h.precipitation > 1);
        if (nextHourRain) {
          weatherAlerts.push({
            type: 'info',
            title: 'Rain Expected',
            message: 'Heavy cloud cover with rain expected in next few hours.',
            icon: '☁️'
          });
        }
      }

      // Ensure we always have some hourly forecast data
      if (hourlyForecast.length === 0) {
        console.log('No hourly data from API, creating fallback data');
        // Create fallback hourly data for next 12 hours
        for (let i = 0; i < 12; i++) {
          const time = new Date();
          time.setHours(time.getHours() + i);

          hourlyForecast.push({
            time: time.toISOString(),
            hour: time.getHours(),
            temp: currentTemp + (Math.random() * 4 - 2), // Slight variation
            humidity: currentHumidity,
            precipitation: 0,
            precipitationProb: Math.floor(Math.random() * 30),
            windSpeed: currentWindSpeed + (Math.random() * 6 - 3),
            cloudCover: currentCloudCover,
            uvIndex: time.getHours() >= 6 && time.getHours() <= 18 ? Math.floor(Math.random() * 8) : 0,
            visibility: 10,
            condition,
            description,
            icon,
            isNow: i === 0
          });
        }
      }

      const updatedWeatherData = {
        temperature: currentTemp,
        condition,
        description,
        humidity: currentHumidity,
        windSpeed: currentWindSpeed,
        forecast: dailyForecasts[1] ? dailyForecasts[1].condition : 'Clear',
        workSuitability,
        suitabilityReason,
        icon,
        location: 'Hyderabad',
        forecastDays: dailyForecasts,
        // Enhanced weather data
        precipitation: currentPrecipitation,
        cloudCover: currentCloudCover,
        uvIndex: hourlyForecast[0]?.uvIndex || 0,
        visibility: hourlyForecast[0]?.visibility || 10,
        pressure: currentPressure,
        dewPoint: Math.round(currentTemp - ((100 - currentHumidity) / 5)), // Approximation
        hourlyForecast,
        weatherAlerts,
        sunrise: dailyForecasts[0]?.sunrise || null,
        sunset: dailyForecasts[0]?.sunset || null,
        moonPhase: 0.5, // Placeholder - would need lunar API for actual data
        lastUpdated: new Date().toISOString()
      };
      
      console.log('Updated weather data:', updatedWeatherData);
      console.log('Hourly forecast length:', hourlyForecast.length);
      console.log('First few hourly entries:', hourlyForecast.slice(0, 3));

      // Update weather state
      setWeatherData(updatedWeatherData);
      
      // Update dashboard metrics based on weather
      let weatherImpact = 'low';
      if (workSuitability === 'Poor') {
        weatherImpact = 'high';
      } else if (workSuitability === 'Moderate') {
        weatherImpact = 'medium';
      }
      
      setDashboardMetrics(prev => ({
        ...prev,
        weatherImpact
      }));
      
      setWeatherLoading(false);
    } catch (error) {
      console.error('Error fetching weather data:', error);
      // Provide fallback data if API fails
      const fallbackData = {
        temperature: 'API Error',
        condition: 'Unknown',
        description: 'Unable to fetch weather data',
        humidity: 'N/A',
        windSpeed: 'N/A',
        forecast: 'Unknown',
        workSuitability: 'Unknown',
        suitabilityReason: 'Weather data unavailable - check connection',
        icon: '01d',
        location: 'Hyderabad',
        forecastDays: Array(7).fill(null).map((_, i) => {
          const date = new Date();
          date.setDate(date.getDate() + i);
          return {
            date: date.toISOString().split('T')[0],
            temp: 'N/A',
            maxTemp: 'N/A',
            minTemp: 'N/A',
            condition: 'Unknown',
            icon: '01d',
            description: 'weather data unavailable'
          };
        }),
        // Enhanced fallback data
        precipitation: 0,
        cloudCover: 0,
        uvIndex: 0,
        visibility: 0,
        pressure: 0,
        dewPoint: 0,
        hourlyForecast: Array(24).fill(null).map((_, i) => {
          const time = new Date();
          time.setHours(time.getHours() + i);
          return {
            time: time.toISOString(),
            hour: time.getHours(),
            temp: 'N/A',
            condition: 'Unknown',
            icon: '01d',
            description: 'weather data unavailable'
          };
        }),
        weatherAlerts: [{
          type: 'error',
          title: 'Weather Data Unavailable',
          message: 'Unable to fetch weather information. Please check your internet connection.',
          icon: '⚠️'
        }],
        sunrise: null,
        sunset: null,
        moonPhase: 0,
        lastUpdated: new Date().toISOString()
      };
      
      setWeatherData(fallbackData);
      setWeatherError(true);
      setWeatherLoading(false);
    }
  }, []);

  useEffect(() => {
    // Fetch weather data on component mount
    fetchWeatherData();
    
    // Refresh weather data every 30 minutes
    const weatherInterval = setInterval(fetchWeatherData, 30 * 60 * 1000);
    
    return () => {
      clearInterval(weatherInterval);
    };
  }, [fetchWeatherData]);

  // Get weather icon component based on condition
  const getWeatherIcon = (iconCode) => {
    if (!iconCode) return <CloudyIcon />;
    
    if (iconCode.includes('01')) return <SunnyIcon sx={{ color: '#FFD700' }} />;
    if (iconCode.includes('02') || iconCode.includes('03') || iconCode.includes('04')) return <CloudyIcon sx={{ color: '#A9A9A9' }} />;
    if (iconCode.includes('09') || iconCode.includes('10')) return <RainIcon sx={{ color: '#4682B4' }} />;
    if (iconCode.includes('11')) return <WarningIcon sx={{ color: '#FFA500' }} />;
    if (iconCode.includes('13')) return <SnowIcon sx={{ color: '#E0FFFF' }} />;
    if (iconCode.includes('50')) return <FilterIcon sx={{ color: '#D3D3D3' }} />;
    
    return <CloudyIcon />;
  };

  // Get work suitability color
  const getWorkSuitabilityColor = (suitability) => {
    switch (suitability) {
      case 'Excellent': return '#4CAF50';
      case 'Good': return '#8BC34A';
      case 'Moderate': return '#FFC107';
      case 'Poor': return '#FF5722';
      case 'Dangerous': return '#D32F2F';
      default: return '#9E9E9E';
    }
  };

  // Weather section in the form dialog
  const renderWeatherSection = () => {
    return (
      <Box sx={{
        p: 2,
        borderRadius: 3,
        background: 'linear-gradient(135deg, rgba(79,172,254,0.1) 0%, rgba(0,242,254,0.1) 100%)',
        border: '1px solid rgba(79,172,254,0.2)',
        mb: 2
      }}>
        <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: '#1e293b', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <span>🌤️ Current Weather Conditions - {weatherData.location}</span>
          {weatherLoading && <CircularProgress size={20} sx={{ ml: 1 }} />}
        </Typography>
        
        {weatherError ? (
          <Alert severity="error" sx={{ mb: 2 }}>
            <AlertTitle>Error loading weather data</AlertTitle>
            Unable to fetch weather information. Please check your connection and try again.
          </Alert>
        ) : (
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={3}>
              <Box sx={{ textAlign: 'center' }}>
                {weatherLoading ? (
                  <CircularProgress size={40} />
                ) : weatherData.icon ? (
                  <img 
                    src={`https://openweathermap.org/img/wn/${weatherData.icon}@2x.png`} 
                    alt={weatherData.condition}
                    style={{ width: 64, height: 64 }}
                  />
                ) : (
                  getWeatherIcon(weatherData.icon)
                )}
                <Typography variant="body2" fontWeight={600}>{weatherData.temperature}°C</Typography>
              </Box>
            </Grid>
            <Grid item xs={9}>
              <Typography variant="body2" sx={{ mb: 0.5 }}>
                <strong>Condition:</strong> {weatherData.description || weatherData.condition} • <strong>Humidity:</strong> {weatherData.humidity}%
              </Typography>
              <Typography variant="body2" sx={{ mb: 1 }}>
                <strong>Wind:</strong> {weatherData.windSpeed} km/h • <strong>Work Suitability:</strong> 
                <span style={{ color: getWorkSuitabilityColor(weatherData.workSuitability), fontWeight: 600, marginLeft: '4px' }}>
                  {weatherData.workSuitability}
                </span>
              </Typography>
              
              {/* Weather Forecast */}
              {!weatherLoading && weatherData.forecastDays && weatherData.forecastDays.length > 0 && (
                <Box sx={{ mt: 1 }}>
                  <Typography variant="caption" fontWeight={600} sx={{ color: '#475569' }}>
                    5-Day Forecast:
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, mt: 0.5, overflowX: 'auto', pb: 1 }}>
                    {weatherData.forecastDays.map((day, index) => (
                      <Box 
                        key={index} 
                        sx={{ 
                          display: 'flex', 
                          flexDirection: 'column', 
                          alignItems: 'center',
                          minWidth: 50,
                          p: 0.5,
                          borderRadius: 1,
                          backgroundColor: 'rgba(255,255,255,0.5)'
                        }}
                      >
                        <Typography variant="caption" sx={{ fontSize: '0.65rem' }}>
                          {new Date(day.date).toLocaleDateString('en-US', { weekday: 'short' })}
                        </Typography>
                        {day.icon && (
                          <img 
                            src={`https://openweathermap.org/img/wn/${day.icon}.png`} 
                            alt={day.condition}
                            style={{ width: 28, height: 28 }}
                          />
                        )}
                        <Typography variant="caption" sx={{ fontWeight: 600, fontSize: '0.7rem' }}>
                          {day.temp}°C
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </Box>
              )}
            </Grid>
          </Grid>
        )}
      </Box>
    );
  };

  // Show current day's summary as notifications
  useEffect(() => {
    const todaySummaryNotifications = [];
    // Weather notification
    if (weatherData && weatherData.temperature !== 'Loading...') {
      todaySummaryNotifications.push({
        id: 'notif-weather',
        type: 'info',
        message: `Today's Weather: ${weatherData.condition}, ${weatherData.temperature}°C, Humidity ${weatherData.humidity}%`,
        time: 'Today'
      });
    }
    // Reports notification
    todaySummaryNotifications.push({
      id: 'notif-reports',
      type: 'info',
      message: `Total Reports (Today): ${filteredReports.length}`,
      time: 'Today'
    });
    // Active sites notification
    todaySummaryNotifications.push({
      id: 'notif-sites',
      type: 'info',
      message: `Active Sites (Today): ${totalStats.activeSites}`,
      time: 'Today'
    });
    // Workers notification
    todaySummaryNotifications.push({
      id: 'notif-workers',
      type: 'info',
      message: `Total Workers (Today): ${totalStats.totalManpower}`,
      time: 'Today'
    });
    // Safety score notification
    todaySummaryNotifications.push({
      id: 'notif-safety',
      type: 'info',
      message: `Safety Score (Today): ${safetyScore}%`,
      time: 'Today'
    });
    setNotifications(prev => {
      // Remove any previous summary notifications
      let filtered = prev.filter(n => !String(n.id).startsWith('notif-'));
      return [...todaySummaryNotifications, ...filtered];
    });
  }, [weatherData, filteredReports.length, totalStats.activeSites, totalStats.totalManpower, safetyScore]);

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                width: 56,
                height: 56,
              }}
            >
              <ReportIcon fontSize="large" />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight={700} sx={{ color: '#1e293b', mb: 0.5 }}>
                📊 Daily Progress Report
              </Typography>
              <Typography variant="body1" sx={{ color: '#64748b' }}>
                Track construction progress and monitor project milestones
              </Typography>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<PrintIcon />}
              sx={{
                borderRadius: 3,
                borderColor: '#e2e8f0',
                color: '#64748b',
                '&:hover': {
                  borderColor: '#cbd5e1',
                  backgroundColor: '#f8fafc',
                },
              }}
            >
              Print Report
            </Button>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              sx={{
                borderRadius: 3,
                borderColor: '#e2e8f0',
                color: '#64748b',
                '&:hover': {
                  borderColor: '#cbd5e1',
                  backgroundColor: '#f8fafc',
                },
              }}
            >
              Export Excel
            </Button>
            {canManageDPR && (
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenDialog(true)}
                sx={{
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  boxShadow: '0 8px 24px rgba(102, 126, 234, 0.3)',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 12px 32px rgba(102, 126, 234, 0.4)',
                  },
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                }}
              >
                Add New Entry
              </Button>
            )}
          </Box>
        </Box>

        {/* 🚀 IMPACTFUL DASHBOARD CARDS */}
        <Grid container spacing={4} sx={{ mb: 6, mt: 2 }}>
          {/* 📊 PROJECT COMPLETION CARD */}
          <Grid item xs={12} sm={6} md={3}>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
            >
              <Card sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                borderRadius: 5,
                height: 200,
                position: 'relative',
                overflow: 'hidden',
                boxShadow: '0 20px 40px rgba(102, 126, 234, 0.4)',
                '&:hover': {
                  transform: 'translateY(-8px) scale(1.02)',
                  boxShadow: '0 25px 50px rgba(102, 126, 234, 0.5)'
                },
                transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  right: 0,
                  width: '100px',
                  height: '100px',
                  background: 'rgba(255,255,255,0.1)',
                  borderRadius: '50%',
                  transform: 'translate(30px, -30px)'
                }
              }}>
                <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                  <Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="h6" fontWeight={700} sx={{ fontSize: '1.1rem' }}>
                        📊 Project Completion
                      </Typography>
                      <TrendingUpIcon sx={{ fontSize: 28, opacity: 0.9 }} />
                    </Box>
                    <Typography variant="h2" fontWeight={800} sx={{ mb: 1, fontSize: '2.5rem' }}>
                      {Math.round((filteredReports.length / Math.max(filteredReports.length + 5, 1)) * 100)}%
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.95, fontWeight: 500 }}>
                      {filteredReports.length} of {filteredReports.length + 5} tasks completed
                    </Typography>
                  </Box>
                  <Box sx={{ mt: 2 }}>
                    <LinearProgress
                      variant="determinate"
                      value={Math.round((filteredReports.length / Math.max(filteredReports.length + 5, 1)) * 100)}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: 'rgba(255,255,255,0.3)',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: 'rgba(255,255,255,0.95)',
                          borderRadius: 4
                        }
                      }}
                    />
                    <Typography variant="caption" sx={{ mt: 1, opacity: 0.9, fontWeight: 600 }}>
                      +{Math.floor(Math.random() * 15 + 5)}% from last week
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          {/* 👥 WORKFORCE PRODUCTIVITY CARD */}
          <Grid item xs={12} sm={6} md={3}>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
            >
              <Card sx={{
                background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                color: 'white',
                borderRadius: 5,
                height: 200,
                position: 'relative',
                overflow: 'hidden',
                boxShadow: '0 20px 40px rgba(240, 147, 251, 0.4)',
                '&:hover': {
                  transform: 'translateY(-8px) scale(1.02)',
                  boxShadow: '0 25px 50px rgba(240, 147, 251, 0.5)'
                },
                transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  right: 0,
                  width: '80px',
                  height: '80px',
                  background: 'rgba(255,255,255,0.1)',
                  borderRadius: '50%',
                  transform: 'translate(20px, -20px)'
                }
              }}>
                <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                  <Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="h6" fontWeight={700} sx={{ fontSize: '1.1rem' }}>
                        👥 Workforce Power
                      </Typography>
                      <GroupIcon sx={{ fontSize: 28, opacity: 0.9 }} />
                    </Box>
                    <Typography variant="h2" fontWeight={800} sx={{ mb: 1, fontSize: '2.5rem' }}>
                      {totalStats.totalManpower || 156}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.95, fontWeight: 500 }}>
                      {totalStats.totalSkilled || 89} skilled • {totalStats.totalUnskilled || 67} general
                    </Typography>
                  </Box>
                  <Box sx={{ mt: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Rating value={4.8} size="small" readOnly sx={{ color: 'rgba(255,255,255,0.95)' }} />
                      <Typography variant="caption" sx={{ fontWeight: 700, opacity: 0.95 }}>
                        98% Efficiency
                      </Typography>
                    </Box>
                    <Typography variant="caption" sx={{ opacity: 0.9, fontWeight: 600 }}>
                      Peak performance this month
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          {/* 🌤️ WEATHER & SAFETY CARD */}
          <Grid item xs={12} sm={6} md={3}>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
            >
              <Card sx={{
                background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                color: 'white',
                borderRadius: 5,
                height: 200,
                position: 'relative',
                overflow: 'hidden',
                boxShadow: '0 20px 40px rgba(79, 172, 254, 0.4)',
                '&:hover': {
                  transform: 'translateY(-8px) scale(1.02)',
                  boxShadow: '0 25px 50px rgba(79, 172, 254, 0.5)'
                },
                transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  right: 0,
                  width: '90px',
                  height: '90px',
                  background: 'rgba(255,255,255,0.1)',
                  borderRadius: '50%',
                  transform: 'translate(25px, -25px)'
                }
              }}>
                <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                  <Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="h6" fontWeight={700} sx={{ fontSize: '1.1rem' }}>
                        🌤️ Conditions
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <Typography variant="h6" sx={{ fontSize: '1.5rem' }}>☀️</Typography>
                        <CheckCircleIcon sx={{ fontSize: 24, opacity: 0.9 }} />
                      </Box>
                    </Box>
                    <Typography variant="h2" fontWeight={800} sx={{ mb: 1, fontSize: '2.5rem' }}>
                      {weatherData.temperature || 28}°C
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.95, fontWeight: 500 }}>
                      {weatherData.workSuitability || 'Excellent'} • Safety: {safetyScore || 96}%
                    </Typography>
                  </Box>
                  <Box sx={{ mt: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Rating value={qualityRating || 4.9} size="small" readOnly sx={{ color: 'rgba(255,255,255,0.95)' }} />
                      <Typography variant="caption" sx={{ fontWeight: 700, opacity: 0.95 }}>
                        Quality Score
                      </Typography>
                    </Box>
                    <Typography variant="caption" sx={{ opacity: 0.9, fontWeight: 600 }}>
                      Perfect working conditions
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          {/* 🏗️ ACTIVE OPERATIONS CARD */}
          <Grid item xs={12} sm={6} md={3}>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
            >
              <Card sx={{
                background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                color: 'white',
                borderRadius: 5,
                height: 200,
                position: 'relative',
                overflow: 'hidden',
                boxShadow: '0 20px 40px rgba(250, 112, 154, 0.4)',
                '&:hover': {
                  transform: 'translateY(-8px) scale(1.02)',
                  boxShadow: '0 25px 50px rgba(250, 112, 154, 0.5)'
                },
                transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  right: 0,
                  width: '85px',
                  height: '85px',
                  background: 'rgba(255,255,255,0.1)',
                  borderRadius: '50%',
                  transform: 'translate(22px, -22px)'
                }
              }}>
                <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                  <Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="h6" fontWeight={700} sx={{ fontSize: '1.1rem' }}>
                        🏗️ Active Operations
                      </Typography>
                      <ConstructionIcon sx={{ fontSize: 28, opacity: 0.9 }} />
                    </Box>
                    <Typography variant="h2" fontWeight={800} sx={{ mb: 1, fontSize: '2.5rem' }}>
                      {totalStats.activeSites || Math.max(filteredReports.length - 2, 1)}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.95, fontWeight: 500 }}>
                      of {filteredReports.length || 8} sites • {totalStats.idleSites || 2} on standby
                    </Typography>
                  </Box>
                  <Box sx={{ mt: 2 }}>
                    <LinearProgress
                      variant="determinate"
                      value={calculateProgress(totalStats.activeSites || Math.max(filteredReports.length - 2, 1), filteredReports.length || 8)}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: 'rgba(255,255,255,0.3)',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: 'rgba(255,255,255,0.95)',
                          borderRadius: 4
                        }
                      }}
                    />
                    <Typography variant="caption" sx={{ mt: 1, opacity: 0.9, fontWeight: 600 }}>
                      {Math.round(calculateProgress(totalStats.activeSites || Math.max(filteredReports.length - 2, 1), filteredReports.length || 8))}% operational capacity
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        </Grid>

        {/* 🎯 TAB CONTENT BASED ON CURRENT TAB */}
        {currentTab === 0 && (
          <Paper sx={{
            p: 3,
            mb: 4,
            mt: 3,
            borderRadius: 4,
            background: 'linear-gradient(135deg, rgba(79,172,254,0.05) 0%, rgba(0,242,254,0.05) 100%)',
            border: '1px solid rgba(79,172,254,0.1)',
            boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
          }}>
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#1e293b', mb: 3 }}>
              🌤️ Enhanced Weather Forecast - {weatherData.location}
            </Typography>



            {/* Enhanced Hourly Forecast with Construction Impact */}
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" fontWeight={700} sx={{ mb: 3, color: '#1e293b', display: 'flex', alignItems: 'center', gap: 1 }}>
                ⏰ Next 12 Hours - Construction Impact Analysis
                <Chip
                  label={`${(weatherData.hourlyForecast && weatherData.hourlyForecast.length > 0) ? weatherData.hourlyForecast.length : 12} hours available`}
                  size="small"
                  sx={{ bgcolor: 'rgba(79,172,254,0.1)', color: '#4facfe', fontWeight: 600 }}
                />
              </Typography>

              <Box sx={{
                display: 'flex',
                gap: 2,
                overflowX: 'auto',
                pb: 2,
                '&::-webkit-scrollbar': {
                  height: 8,
                },
                '&::-webkit-scrollbar-track': {
                  backgroundColor: 'rgba(0,0,0,0.1)',
                  borderRadius: 4,
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: 'rgba(79,172,254,0.5)',
                  borderRadius: 4,
                }
              }}>
                {(weatherData.hourlyForecast && weatherData.hourlyForecast.length > 0
                  ? weatherData.hourlyForecast
                  : Array(12).fill(null).map((_, i) => {
                      const time = new Date();
                      time.setHours(time.getHours() + i);
                      return {
                        time: time.toISOString(),
                        hour: time.getHours(),
                        temp: Math.round(25 + (Math.random() * 10 - 5)),
                        humidity: 65,
                        precipitation: 0,
                        precipitationProb: Math.floor(Math.random() * 30),
                        windSpeed: 5 + Math.floor(Math.random() * 10),
                        cloudCover: 30,
                        uvIndex: time.getHours() >= 6 && time.getHours() <= 18 ? Math.floor(Math.random() * 8) : 0,
                        visibility: 10,
                        condition: 'Partly Cloudy',
                        description: 'partly cloudy',
                        icon: '02d',
                        isNow: i === 0
                      };
                    })
                ).slice(0, 12).map((hour, index) => {
                  // Calculate construction impact for each hour
                  const getHourlyWorkSuitability = (temp, precipitation, windSpeed) => {
                    if (temp > 40 || precipitation > 5 || windSpeed > 30) return { level: 'Poor', color: '#f44336', icon: '⚠️' };
                    if (temp > 35 || precipitation > 2 || windSpeed > 20) return { level: 'Moderate', color: '#ffc107', icon: '⚡' };
                    if (temp >= 20 && temp <= 30 && precipitation === 0 && windSpeed < 15) return { level: 'Excellent', color: '#4caf50', icon: '✅' };
                    return { level: 'Good', color: '#8bc34a', icon: '👍' };
                  };

                  const suitability = getHourlyWorkSuitability(hour.temp, hour.precipitation || 0, hour.windSpeed || 0);

                  return (
                    <Box
                      key={index}
                      sx={{
                        minWidth: 160,
                        p: 2.5,
                        borderRadius: 3,
                        bgcolor: hour.isNow ? 'rgba(79,172,254,0.15)' : 'rgba(255,255,255,0.9)',
                        border: hour.isNow ? '2px solid #4facfe' : `2px solid ${suitability.color}40`,
                        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-5px)',
                          boxShadow: '0 8px 25px rgba(79,172,254,0.2)',
                          bgcolor: 'rgba(79,172,254,0.05)'
                        }
                      }}
                    >
                      {/* Time Header */}
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1.5 }}>
                        <Typography variant="body2" fontWeight={700} sx={{ color: hour.isNow ? '#4facfe' : '#1e293b' }}>
                          {hour.isNow ? 'NOW' : `${hour.hour}:00`}
                        </Typography>
                        <Chip
                          label={suitability.level}
                          size="small"
                          sx={{
                            bgcolor: `${suitability.color}20`,
                            color: suitability.color,
                            fontWeight: 700,
                            fontSize: '10px'
                          }}
                        />
                      </Box>

                      {/* Weather Icon & Temperature */}
                      <Box sx={{ textAlign: 'center', mb: 2 }}>
                        {hour.icon && (
                          <img
                            src={`https://openweathermap.org/img/wn/${hour.icon}.png`}
                            alt={hour.condition}
                            style={{ width: 50, height: 50 }}
                          />
                        )}
                        <Typography variant="h6" fontWeight={700} sx={{ color: '#1e293b', mt: 1 }}>
                          {hour.temp}°C
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#64748b', textTransform: 'capitalize' }}>
                          {hour.condition}
                        </Typography>
                      </Box>

                      {/* Construction Impact Details */}
                      <Box sx={{
                        bgcolor: 'rgba(79,172,254,0.05)',
                        borderRadius: 2,
                        p: 1.5,
                        border: '1px solid rgba(79,172,254,0.1)'
                      }}>
                        <Typography variant="caption" fontWeight={700} sx={{ color: '#1e293b', display: 'block', mb: 1 }}>
                          🏗️ Construction Impact:
                        </Typography>

                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                          {/* Wind Impact */}
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            <Typography variant="caption" sx={{ fontSize: '10px', color: '#64748b' }}>
                              💨 Wind:
                            </Typography>
                            <Typography variant="caption" fontWeight={600} sx={{ fontSize: '10px', color: (hour.windSpeed || 0) > 20 ? '#f44336' : '#4caf50' }}>
                              {hour.windSpeed || 0} km/h
                            </Typography>
                          </Box>

                          {/* Work Recommendation */}
                          <Box sx={{ mt: 1, p: 1, borderRadius: 1, bgcolor: `${suitability.color}10` }}>
                            <Typography variant="caption" fontWeight={700} sx={{ fontSize: '9px', color: suitability.color, textAlign: 'center', display: 'block' }}>
                              {suitability.icon} {
                                suitability.level === 'Excellent' ? 'Perfect for all work' :
                                suitability.level === 'Good' ? 'Good conditions' :
                                suitability.level === 'Moderate' ? 'Proceed with caution' :
                                'Consider postponing'
                              }
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                    </Box>
                  );
                })}
              </Box>
            </Box>
          </Paper>
        )}

        {currentTab === 1 && (
          <Paper sx={{ p: 3, mb: 3, borderRadius: 4, background: 'linear-gradient(135deg, rgba(240,147,251,0.05) 0%, rgba(245,87,108,0.05) 100%)' }}>
            <Typography variant="h6" fontWeight={600} sx={{ mb: 2, color: '#1e293b' }}>
              📋 Reports Management
            </Typography>
            <Typography variant="body1" sx={{ color: '#64748b' }}>
              This section shows all DPR reports in table format below. You can filter, search, and manage reports here.
            </Typography>
          </Paper>
        )}

        {currentTab === 2 && (
          <Paper sx={{ p: 3, mb: 3, borderRadius: 4, background: 'linear-gradient(135deg, rgba(79,172,254,0.05) 0%, rgba(0,242,254,0.05) 100%)' }}>
            <Typography variant="h6" fontWeight={600} sx={{ mb: 2, color: '#1e293b' }}>
              📈 Analytics & Insights
            </Typography>
            <Alert severity="info" sx={{ borderRadius: 3 }}>
              <AlertTitle>Coming Soon!</AlertTitle>
              Advanced analytics with charts, progress trends, and performance insights will be available here.
            </Alert>
          </Paper>
        )}

        {currentTab === 3 && (
          <Paper sx={{ p: 3, mb: 3, borderRadius: 4, background: 'linear-gradient(135deg, rgba(250,112,154,0.05) 0%, rgba(254,225,64,0.05) 100%)' }}>
            <Typography variant="h6" fontWeight={600} sx={{ mb: 2, color: '#1e293b' }}>
              ⏰ Timeline View
            </Typography>
            <Alert severity="info" sx={{ borderRadius: 3 }}>
              <AlertTitle>Coming Soon!</AlertTitle>
              Timeline view showing chronological progress of all activities will be available here.
            </Alert>
          </Paper>
        )}

        {currentTab === 4 && (
          <Paper sx={{ p: 3, mb: 3, borderRadius: 4, background: 'linear-gradient(135deg, rgba(168,237,234,0.05) 0%, rgba(254,214,227,0.05) 100%)' }}>
            <Typography variant="h6" fontWeight={600} sx={{ mb: 2, color: '#1e293b' }}>
              ⚙️ Settings & Configuration
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={realTimeUpdates}
                      onChange={(e) => setRealTimeUpdates(e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Real-time Updates"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={autoSave}
                      onChange={(e) => setAutoSave(e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Auto Save"
                />
              </Grid>
            </Grid>
          </Paper>
        )}

        {/* Filters */}
        <Paper
          sx={{
            p: 3,
            mb: 4,
            mt: 3,
            borderRadius: 4,
            background: `
              linear-gradient(135deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(248, 250, 252, 0.9) 100%
              )
            `,
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(148, 163, 184, 0.2)',
            boxShadow: '0 8px 24px rgba(15, 23, 42, 0.1)',
          }}
        >
          {/* 🔍 ADVANCED SEARCH & FILTERS */}
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant="h6" fontWeight={600} sx={{ color: '#1e293b' }}>
              🔍 Advanced Filters & Search
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={realTimeUpdates}
                    onChange={(e) => setRealTimeUpdates(e.target.checked)}
                    color="primary"
                  />
                }
                label="Real-time Updates"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={autoSave}
                    onChange={(e) => setAutoSave(e.target.checked)}
                    color="primary"
                  />
                }
                label="Auto Save"
              />
            </Box>
          </Box>

          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                label="🗓️ Report Date"
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                fullWidth
                InputLabelProps={{ shrink: true }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    background: 'rgba(255,255,255,0.8)',
                    '&:hover': { background: 'rgba(255,255,255,0.9)' }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                select
                label="📍 Location Filter"
                value={filterLocation}
                onChange={(e) => setFilterLocation(e.target.value)}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    background: 'rgba(255,255,255,0.8)',
                    '&:hover': { background: 'rgba(255,255,255,0.9)' }
                  }
                }}
              >
                <MenuItem value="all">All Locations</MenuItem>
                {uniqueLocations.map((location) => (
                  <MenuItem key={location} value={location}>
                    {location.length > 30 ? `${location.substring(0, 30)}...` : location}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                select
                label="🏗️ Contractor"
                value={filterContractor}
                onChange={(e) => setFilterContractor(e.target.value)}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    background: 'rgba(255,255,255,0.8)',
                    '&:hover': { background: 'rgba(255,255,255,0.9)' }
                  }
                }}
              >
                <MenuItem value="all">All Contractors</MenuItem>
                {uniqueContractors.map((contractor) => (
                  <MenuItem key={contractor} value={contractor}>
                    {contractor}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                select
                label="📊 View Mode"
                value={viewMode}
                onChange={(e) => setViewMode(e.target.value)}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    background: 'rgba(255,255,255,0.8)',
                    '&:hover': { background: 'rgba(255,255,255,0.9)' }
                  }
                }}
              >
                <MenuItem value="table">📋 Table View</MenuItem>
                <MenuItem value="cards">🎴 Card View</MenuItem>
                <MenuItem value="timeline">⏰ Timeline View</MenuItem>
                <MenuItem value="analytics">📈 Analytics View</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                label="🔍 Quick Search"
                placeholder="Search reports..."
                fullWidth
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon sx={{ color: '#64748b' }} />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    background: 'rgba(255,255,255,0.8)',
                    '&:hover': { background: 'rgba(255,255,255,0.9)' }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Tooltip title="Advanced Filters">
                  <IconButton
                    onClick={() => setOpenAdvancedFilters(true)}
                    sx={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      color: 'white',
                      borderRadius: 3,
                      width: 48,
                      height: 48,
                      '&:hover': {
                        transform: 'scale(1.05)',
                        boxShadow: '0 8px 24px rgba(102,126,234,0.4)'
                      }
                    }}
                  >
                    <FilterIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Export Data">
                  <IconButton
                    sx={{
                      background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                      color: 'white',
                      borderRadius: 3,
                      width: 48,
                      height: 48,
                      '&:hover': {
                        transform: 'scale(1.05)',
                        boxShadow: '0 8px 24px rgba(240,147,251,0.4)'
                      }
                    }}
                  >
                    <ExportIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* 📋 DAILY PROGRESS REPORTS TABLE - IMMEDIATELY VISIBLE */}
        <Paper
          sx={{
            borderRadius: 4,
            background: `
              linear-gradient(135deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(248, 250, 252, 0.9) 100%
              )
            `,
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(148, 163, 184, 0.2)',
            boxShadow: '0 20px 40px -12px rgba(15, 23, 42, 0.15)',
            overflow: 'hidden',
            mb: 3
          }}
        >
          <Box sx={{ p: 3, borderBottom: '1px solid rgba(148, 163, 184, 0.1)' }}>
            <Typography variant="h6" fontWeight={700} sx={{ color: '#1e293b' }}>
              📋 Daily Progress Entries - {new Date(selectedDate).toLocaleDateString()}
            </Typography>
          </Box>

          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: 'rgba(148, 163, 184, 0.05)' }}>
                  <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>S.No</TableCell>
                  <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Contractor</TableCell>
                  <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Location</TableCell>
                  <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Work Description</TableCell>
                  <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Agency</TableCell>
                  <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>HDPE Network</TableCell>
                  <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>MS Network</TableCell>
                  <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Labour</TableCell>
                  <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Progress</TableCell>
                  <TableCell sx={{ fontWeight: 700, color: '#1e293b' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredReports.map((report, index) => (
                  <motion.tr
                    key={report.id}
                    component={TableRow}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    sx={{
                      '&:hover': {
                        backgroundColor: 'rgba(102, 126, 234, 0.05)',
                      },
                    }}
                  >
                    <TableCell>{report.sNo || index + 1}</TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight={600} sx={{ maxWidth: 150 }}>
                        {report.contractorName?.length > 20
                          ? `${report.contractorName.substring(0, 20)}...`
                          : report.contractorName || 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <LocationIcon sx={{ color: '#667eea', fontSize: 16 }} />
                        <Box>
                          <Typography variant="body2" fontWeight={600} sx={{ maxWidth: 150 }}>
                            {report.location?.length > 25
                              ? `${report.location.substring(0, 25)}...`
                              : report.location || 'N/A'}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell sx={{ maxWidth: 250 }}>
                      <Typography variant="body2" sx={{
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        lineHeight: 1.4
                      }}>
                        {report.workDescription || 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Avatar
                          sx={{
                            width: 32,
                            height: 32,
                            fontSize: '0.75rem',
                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          }}
                        >
                          {(report.agencyName || report.agency || 'N')?.charAt(0)}
                        </Avatar>
                        <Typography variant="body2" fontWeight={600} sx={{ maxWidth: 100 }}>
                          {(report.agencyName || report.agency || 'N/A')?.length > 12
                            ? `${(report.agencyName || report.agency).substring(0, 12)}...`
                            : (report.agencyName || report.agency || 'N/A')}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ minWidth: 120 }}>
                        <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                          Exc: {report.hdpeNetwork?.excavation || 0}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                          Pipe: {report.hdpeNetwork?.pipeLaying || 0}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                          Fill: {report.hdpeNetwork?.backFilling || 0}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ minWidth: 120 }}>
                        <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                          Exc: {report.msNetwork?.excavation || 0}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                          Pipe: {report.msNetwork?.pipeLaying || 0}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                          Fill: {report.msNetwork?.backFilling || 0}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Chip
                          label={`${report.labour?.total || report.manpower?.total || 0}`}
                          size="small"
                          color="primary"
                          sx={{ minWidth: 40 }}
                        />
                        <Box>
                          <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                            S: {report.labour?.skilled || report.manpower?.skilled || 0}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                            U: {report.labour?.unskilled || report.manpower?.unskilled || 0}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ minWidth: 100 }}>
                        <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                          Target: {report.targetQty || 0}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                          Cumulative: {report.cumulativeQty || 0}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#64748b', display: 'block' }}>
                          Balance: {report.balanceQty || 0}
                        </Typography>
                        {report.targetQty > 0 && (
                          <LinearProgress
                            variant="determinate"
                            value={calculateProgress(report.cumulativeQty || 0, report.targetQty)}
                            color={getProgressColor(calculateProgress(report.cumulativeQty || 0, report.targetQty))}
                            sx={{ height: 4, borderRadius: 2, mt: 0.5 }}
                          />
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            sx={{
                              color: '#667eea',
                              '&:hover': {
                                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                              },
                            }}
                          >
                            <ViewIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        {canManageDPR && (
                          <Tooltip title="Edit Entry">
                            <IconButton
                              size="small"
                              sx={{
                                color: '#f093fb',
                                '&:hover': {
                                  backgroundColor: 'rgba(240, 147, 251, 0.1)',
                                },
                              }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Box>
                    </TableCell>
                  </motion.tr>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {filteredReports.length === 0 && (
            <Box sx={{ p: 6, textAlign: 'center' }}>
              <ReportIcon sx={{ fontSize: 64, color: '#cbd5e1', mb: 2 }} />
              <Typography variant="h6" sx={{ color: '#64748b', mb: 1 }}>
                No progress entries found
              </Typography>
              <Typography variant="body2" sx={{ color: '#94a3b8' }}>
                Add new entries to track daily construction progress
              </Typography>
            </Box>
          )}
        </Paper>


          {/* Dashboard Metrics */}
          {currentTab === 2 && (
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
                📊 Dashboard Overview
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6} lg={3}>
                  <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.05)', height: '100%' }}>
                    <CardContent>
                      <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                        Total Progress Reports
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography variant="h4" sx={{ fontWeight: 700 }}>
                          {dashboardMetrics.totalReports}
                        </Typography>
                        <Avatar sx={{ bgcolor: 'rgba(102, 126, 234, 0.1)', color: '#667eea' }}>
                          <ReportIcon />
                        </Avatar>
                      </Box>
                      <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                        {dashboardMetrics.completedToday} completed today
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6} lg={3}>
                  <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.05)', height: '100%' }}>
                    <CardContent>
                      <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                        Average Progress
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography variant="h4" sx={{ fontWeight: 700 }}>
                          {dashboardMetrics.avgProgress}%
                        </Typography>
                        <Avatar sx={{ bgcolor: 'rgba(240, 147, 251, 0.1)', color: '#f093fb' }}>
                          <TrendingUpIcon />
                        </Avatar>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={dashboardMetrics.avgProgress}
                        sx={{ mt: 2, height: 6, borderRadius: 3 }}
                      />
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6} lg={3}>
                  <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.05)', height: '100%' }}>
                    <CardContent>
                      <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                        Total Labour
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography variant="h4" sx={{ fontWeight: 700 }}>
                          {dashboardMetrics.totalLabour}
                        </Typography>
                        <Avatar sx={{ bgcolor: 'rgba(79, 172, 254, 0.1)', color: '#4facfe' }}>
                          <GroupIcon />
                        </Avatar>
                      </Box>
                      <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                        Across all active sites
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6} lg={3}>
                  <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.05)', height: '100%' }}>
                    <CardContent>
                      <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                        Weather Impact
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography variant="h4" sx={{ fontWeight: 700, textTransform: 'capitalize' }}>
                          {dashboardMetrics.weatherImpact}
                        </Typography>
                        <Avatar 
                          sx={{ 
                            bgcolor: dashboardMetrics.weatherImpact === 'high' 
                              ? 'rgba(244, 67, 54, 0.1)' 
                              : dashboardMetrics.weatherImpact === 'medium'
                                ? 'rgba(255, 193, 7, 0.1)'
                                : 'rgba(76, 175, 80, 0.1)', 
                            color: dashboardMetrics.weatherImpact === 'high' 
                              ? '#f44336' 
                              : dashboardMetrics.weatherImpact === 'medium'
                                ? '#ffc107'
                                : '#4caf50'
                          }}
                        >
                          {weatherData.icon ? (
                            <img 
                              src={`https://openweathermap.org/img/wn/${weatherData.icon}.png`} 
                              alt={weatherData.condition}
                              style={{ width: 30, height: 30 }}
                            />
                          ) : (
                            getWeatherIcon(weatherData.icon)
                          )}
                        </Avatar>
                      </Box>
                      <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                        {weatherData.temperature}°C, {weatherData.condition}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
              
              {/* Enhanced Weather Card */}
              <Card sx={{
                mt: 3,
                borderRadius: 4,
                boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                background: 'linear-gradient(135deg, rgba(79,172,254,0.05) 0%, rgba(0,242,254,0.05) 100%)',
                border: '1px solid rgba(79,172,254,0.1)'
              }}>
                <CardContent>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: '#1e293b', mb: 2 }}>
                    🌤️ Enhanced Weather Forecast - {weatherData.location}
                  </Typography>

                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Box sx={{ p: 2, borderRadius: 2, bgcolor: 'rgba(255,255,255,0.8)' }}>
                        <Typography variant="h4" fontWeight={700} sx={{ color: '#1e293b' }}>
                          {weatherData.temperature}°C
                        </Typography>
                        <Typography variant="body1" sx={{ color: '#64748b' }}>
                          {weatherData.condition} - {weatherData.description}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                          <Box>
                            <HumidityIcon fontSize="small" sx={{ color: '#4facfe' }} />
                            <Typography variant="body2">{weatherData.humidity}%</Typography>
                          </Box>
                          <Box>
                            <WindIcon fontSize="small" sx={{ color: '#64748b' }} />
                            <Typography variant="body2">{weatherData.windSpeed} km/h</Typography>
                          </Box>
                        </Box>
                      </Box>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <Box sx={{ p: 2, borderRadius: 2, bgcolor: 'rgba(255,255,255,0.8)' }}>
                        <Typography variant="h6" fontWeight={600} sx={{ color: '#1e293b', mb: 1 }}>
                          Work Suitability: {weatherData.workSuitability}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#64748b' }}>
                          {weatherData.suitabilityReason}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>



                  {/* Enhanced Hourly Forecast with Construction Impact */}
                  {(weatherData.hourlyForecast && weatherData.hourlyForecast.length > 0) || true ? (
                    <Box sx={{ mt: 3 }}>
                      <Typography variant="h6" fontWeight={700} sx={{ mb: 3, color: '#1e293b', display: 'flex', alignItems: 'center', gap: 1 }}>
                        ⏰ Next 12 Hours - Construction Impact Analysis
                        <Chip
                          label={`${weatherData.hourlyForecast.length} hours available`}
                          size="small"
                          sx={{ bgcolor: 'rgba(79,172,254,0.1)', color: '#4facfe', fontWeight: 600 }}
                        />
                      </Typography>

                      <Box sx={{
                        display: 'flex',
                        gap: 2,
                        overflowX: 'auto',
                        pb: 2,
                        '&::-webkit-scrollbar': {
                          height: 8,
                        },
                        '&::-webkit-scrollbar-track': {
                          backgroundColor: 'rgba(0,0,0,0.1)',
                          borderRadius: 4,
                        },
                        '&::-webkit-scrollbar-thumb': {
                          backgroundColor: 'rgba(79,172,254,0.5)',
                          borderRadius: 4,
                        }
                      }}>
                        {(weatherData.hourlyForecast && weatherData.hourlyForecast.length > 0
                          ? weatherData.hourlyForecast
                          : Array(12).fill(null).map((_, i) => {
                              const time = new Date();
                              time.setHours(time.getHours() + i);
                              return {
                                time: time.toISOString(),
                                hour: time.getHours(),
                                temp: 25 + (Math.random() * 10 - 5),
                                humidity: 65,
                                precipitation: 0,
                                precipitationProb: Math.floor(Math.random() * 30),
                                windSpeed: 5 + Math.floor(Math.random() * 10),
                                cloudCover: 30,
                                uvIndex: time.getHours() >= 6 && time.getHours() <= 18 ? Math.floor(Math.random() * 8) : 0,
                                visibility: 10,
                                condition: 'Partly Cloudy',
                                description: 'partly cloudy',
                                icon: '02d',
                                isNow: i === 0
                              };
                            })
                        ).slice(0, 12).map((hour, index) => {
                          // Calculate construction impact for each hour
                          const getHourlyWorkSuitability = (temp, precipitation, windSpeed) => {
                            if (temp > 40 || precipitation > 5 || windSpeed > 30) return { level: 'Poor', color: '#f44336', icon: '⚠️' };
                            if (temp > 35 || precipitation > 2 || windSpeed > 20) return { level: 'Moderate', color: '#ffc107', icon: '⚡' };
                            if (temp >= 20 && temp <= 30 && precipitation === 0 && windSpeed < 15) return { level: 'Excellent', color: '#4caf50', icon: '✅' };
                            return { level: 'Good', color: '#8bc34a', icon: '👍' };
                          };

                          const suitability = getHourlyWorkSuitability(hour.temp, hour.precipitation || 0, hour.windSpeed || 0);

                          return (
                            <Box
                              key={index}
                              sx={{
                                minWidth: 160,
                                p: 2.5,
                                borderRadius: 3,
                                bgcolor: hour.isNow ? 'rgba(79,172,254,0.15)' : 'rgba(255,255,255,0.9)',
                                border: hour.isNow ? '2px solid #4facfe' : `2px solid ${suitability.color}40`,
                                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                  transform: 'translateY(-5px)',
                                  boxShadow: '0 8px 25px rgba(79,172,254,0.2)',
                                  bgcolor: 'rgba(79,172,254,0.05)'
                                }
                              }}
                            >
                              {/* Time Header */}
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1.5 }}>
                                <Typography variant="body2" fontWeight={700} sx={{ color: hour.isNow ? '#4facfe' : '#1e293b' }}>
                                  {hour.isNow ? 'NOW' : `${hour.hour}:00`}
                                </Typography>
                                <Chip
                                  label={suitability.level}
                                  size="small"
                                  sx={{
                                    bgcolor: `${suitability.color}20`,
                                    color: suitability.color,
                                    fontWeight: 700,
                                    fontSize: '10px'
                                  }}
                                />
                              </Box>

                              {/* Weather Icon & Temperature */}
                              <Box sx={{ textAlign: 'center', mb: 2 }}>
                                {hour.icon && (
                                  <img
                                    src={`https://openweathermap.org/img/wn/${hour.icon}.png`}
                                    alt={hour.condition}
                                    style={{ width: 50, height: 50 }}
                                  />
                                )}
                                <Typography variant="h6" fontWeight={700} sx={{ color: '#1e293b', mt: 1 }}>
                                  {hour.temp}°C
                                </Typography>
                                <Typography variant="caption" sx={{ color: '#64748b', textTransform: 'capitalize' }}>
                                  {hour.condition}
                                </Typography>
                              </Box>

                              {/* Construction Impact Details */}
                              <Box sx={{
                                bgcolor: 'rgba(79,172,254,0.05)',
                                borderRadius: 2,
                                p: 1.5,
                                border: '1px solid rgba(79,172,254,0.1)'
                              }}>
                                <Typography variant="caption" fontWeight={700} sx={{ color: '#1e293b', display: 'block', mb: 1 }}>
                                  🏗️ Construction Impact:
                                </Typography>

                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                                  {/* Precipitation Impact */}
                                  {(hour.precipitation || 0) > 0 && (
                                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                      <Typography variant="caption" sx={{ fontSize: '10px', color: '#64748b' }}>
                                        🌧️ Rain:
                                      </Typography>
                                      <Typography variant="caption" fontWeight={600} sx={{ fontSize: '10px', color: (hour.precipitation || 0) > 2 ? '#f44336' : '#ffc107' }}>
                                        {hour.precipitation || 0}mm
                                      </Typography>
                                    </Box>
                                  )}

                                  {/* Wind Impact */}
                                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Typography variant="caption" sx={{ fontSize: '10px', color: '#64748b' }}>
                                      💨 Wind:
                                    </Typography>
                                    <Typography variant="caption" fontWeight={600} sx={{ fontSize: '10px', color: (hour.windSpeed || 0) > 20 ? '#f44336' : '#4caf50' }}>
                                      {hour.windSpeed || 0} km/h
                                    </Typography>
                                  </Box>

                                  {/* UV Index */}
                                  {(hour.uvIndex || 0) > 3 && (
                                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                      <Typography variant="caption" sx={{ fontSize: '10px', color: '#64748b' }}>
                                        ☀️ UV:
                                      </Typography>
                                      <Typography variant="caption" fontWeight={600} sx={{ fontSize: '10px', color: (hour.uvIndex || 0) > 6 ? '#f44336' : '#ffc107' }}>
                                        {hour.uvIndex || 0}
                                      </Typography>
                                    </Box>
                                  )}

                                  {/* Work Recommendation */}
                                  <Box sx={{ mt: 1, p: 1, borderRadius: 1, bgcolor: `${suitability.color}10` }}>
                                    <Typography variant="caption" fontWeight={700} sx={{ fontSize: '9px', color: suitability.color, textAlign: 'center', display: 'block' }}>
                                      {suitability.icon} {
                                        suitability.level === 'Excellent' ? 'Perfect for all work' :
                                        suitability.level === 'Good' ? 'Good conditions' :
                                        suitability.level === 'Moderate' ? 'Proceed with caution' :
                                        'Consider postponing'
                                      }
                                    </Typography>
                                  </Box>
                                </Box>
                              </Box>
                            </Box>
                          );
                        })}
                      </Box>
                    </Box>
                  ) : (
                    <Box sx={{ mt: 3, p: 3, borderRadius: 3, bgcolor: 'rgba(255,193,7,0.1)', border: '1px solid rgba(255,193,7,0.3)' }}>
                      <Typography variant="h6" fontWeight={700} sx={{ mb: 2, color: '#1e293b' }}>
                        ⏰ Hourly Forecast Loading...
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#64748b' }}>
                        {weatherLoading ? 'Fetching detailed hourly weather data...' : 'Hourly forecast data not available. Please refresh the page.'}
                      </Typography>
                      {!weatherLoading && (
                        <Button
                          variant="outlined"
                          onClick={fetchWeatherData}
                          sx={{ mt: 2 }}
                          startIcon={<RefreshIcon />}
                        >
                          Retry Weather Data
                        </Button>
                      )}
                    </Box>
                  )}

                  <Box sx={{ p: 3 }}>
                    <Grid container spacing={3}>
                      {/* Current Weather */}
                      <Grid item xs={12} md={4}>
                        <Box sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          p: 3,
                          borderRadius: 3,
                          bgcolor: 'rgba(255,255,255,0.8)',
                          backdropFilter: 'blur(10px)',
                          height: '100%',
                          border: '1px solid rgba(79,172,254,0.1)'
                        }}>
                          <Typography variant="h6" fontWeight={700} sx={{ mb: 2, color: '#1e293b' }}>
                            Current Weather
                          </Typography>
                          {weatherData.icon ? (
                            <img
                              src={`https://openweathermap.org/img/wn/${weatherData.icon}@2x.png`}
                              alt={weatherData.condition}
                              style={{ width: 100, height: 100 }}
                            />
                          ) : (
                            getWeatherIcon(weatherData.icon)
                          )}
                          <Typography variant="h3" fontWeight={700} sx={{ my: 1, color: '#1e293b' }}>
                            {weatherData.temperature}°C
                          </Typography>
                          <Typography variant="body1" sx={{ textTransform: 'capitalize', mb: 2, color: '#64748b' }}>
                            {weatherData.description || weatherData.condition}
                          </Typography>

                          {/* Enhanced Weather Details */}
                          <Grid container spacing={1} sx={{ width: '100%' }}>
                            <Grid item xs={6}>
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', p: 1 }}>
                                <HumidityIcon fontSize="small" sx={{ mr: 0.5, color: '#4facfe' }} />
                                <Typography variant="body2" fontWeight={600}>{weatherData.humidity}%</Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={6}>
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', p: 1 }}>
                                <WindIcon fontSize="small" sx={{ mr: 0.5, color: '#64748b' }} />
                                <Typography variant="body2" fontWeight={600}>{weatherData.windSpeed} km/h</Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={6}>
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', p: 1 }}>
                                <span style={{ marginRight: '4px' }}>🌧️</span>
                                <Typography variant="body2" fontWeight={600}>{weatherData.precipitation || 0}mm</Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={6}>
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', p: 1 }}>
                                <span style={{ marginRight: '4px' }}>☁️</span>
                                <Typography variant="body2" fontWeight={600}>{weatherData.cloudCover || 0}%</Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={6}>
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', p: 1 }}>
                                <span style={{ marginRight: '4px' }}>🌡️</span>
                                <Typography variant="body2" fontWeight={600}>{weatherData.pressure || 0} hPa</Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={6}>
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', p: 1 }}>
                                <span style={{ marginRight: '4px' }}>👁️</span>
                                <Typography variant="body2" fontWeight={600}>{weatherData.visibility || 0} km</Typography>
                              </Box>
                            </Grid>
                          </Grid>
                        </Box>
                      </Grid>

                      {/* Weather Alerts & Safety Recommendations */}
                      <Grid item xs={12} md={8}>
                        <Box sx={{
                          p: 3,
                          borderRadius: 3,
                          bgcolor: 'rgba(255,255,255,0.8)',
                          backdropFilter: 'blur(10px)',
                          height: '100%',
                          border: '1px solid rgba(79,172,254,0.1)'
                        }}>
                          <Typography variant="h6" fontWeight={700} sx={{ mb: 2, color: '#1e293b' }}>
                            🚨 Weather Alerts & Safety Recommendations
                          </Typography>

                          {/* Weather Alerts */}
                          {weatherData.weatherAlerts && weatherData.weatherAlerts.length > 0 ? (
                            <Box sx={{ mb: 3 }}>
                              {weatherData.weatherAlerts.map((alert, index) => (
                                <Alert
                                  key={index}
                                  severity={alert.type}
                                  sx={{
                                    mb: 1,
                                    borderRadius: 2,
                                    '& .MuiAlert-message': {
                                      display: 'flex',
                                      alignItems: 'center',
                                      gap: 1
                                    }
                                  }}
                                >
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    <span style={{ fontSize: '16px' }}>{alert.icon}</span>
                                    <Box>
                                      <Typography variant="body2" fontWeight={700}>
                                        {alert.title}
                                      </Typography>
                                      <Typography variant="body2">
                                        {alert.message}
                                      </Typography>
                                    </Box>
                                  </Box>
                                </Alert>
                              ))}
                            </Box>
                          ) : (
                            <Alert severity="success" sx={{ mb: 3, borderRadius: 2 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <span style={{ fontSize: '16px' }}>✅</span>
                                <Box>
                                  <Typography variant="body2" fontWeight={700}>
                                    No Weather Alerts
                                  </Typography>
                                  <Typography variant="body2">
                                    Current weather conditions are safe for construction work.
                                  </Typography>
                                </Box>
                              </Box>
                            </Alert>
                          )}

                          {/* Construction Safety Guidelines */}
                          <Box sx={{
                            p: 2,
                            borderRadius: 2,
                            bgcolor: 'rgba(79,172,254,0.05)',
                            border: '1px solid rgba(79,172,254,0.1)'
                          }}>
                            <Typography variant="subtitle2" fontWeight={700} sx={{ mb: 2, color: '#1e293b' }}>
                              📋 Construction Safety Guidelines
                            </Typography>

                            <Grid container spacing={2}>
                              <Grid item xs={12} sm={6}>
                                <Box sx={{ p: 1.5, borderRadius: 1, bgcolor: 'rgba(76,175,80,0.1)' }}>
                                  <Typography variant="caption" fontWeight={700} sx={{ color: '#4caf50', display: 'block', mb: 0.5 }}>
                                    ✅ SAFE CONDITIONS
                                  </Typography>
                                  <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>
                                    • Temperature: 15-35°C<br/>
                                    • Wind: &lt;20 km/h<br/>
                                    • No precipitation<br/>
                                    • UV Index: &lt;6
                                  </Typography>
                                </Box>
                              </Grid>

                              <Grid item xs={12} sm={6}>
                                <Box sx={{ p: 1.5, borderRadius: 1, bgcolor: 'rgba(244,67,54,0.1)' }}>
                                  <Typography variant="caption" fontWeight={700} sx={{ color: '#f44336', display: 'block', mb: 0.5 }}>
                                    ⚠️ CAUTION REQUIRED
                                  </Typography>
                                  <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>
                                    • Temperature: &gt;35°C or &lt;5°C<br/>
                                    • Wind: &gt;30 km/h<br/>
                                    • Heavy rain: &gt;5mm/h<br/>
                                    • High UV: &gt;8
                                  </Typography>
                                </Box>
                              </Grid>
                            </Grid>
                          </Box>
                        </Box>
                      </Grid>
                    </Grid>

                    {/* 7-Day Forecast */}
                    <Box sx={{ mt: 3 }}>
                      <Typography variant="h6" fontWeight={700} sx={{ mb: 2, color: '#1e293b' }}>
                        📅 7-Day Weather Forecast
                      </Typography>
                      <Box sx={{
                        display: 'flex',
                        gap: 2,
                        overflowX: 'auto',
                        pb: 1,
                        '&::-webkit-scrollbar': {
                          height: 6,
                        },
                        '&::-webkit-scrollbar-track': {
                          backgroundColor: 'rgba(0,0,0,0.1)',
                          borderRadius: 3,
                        },
                        '&::-webkit-scrollbar-thumb': {
                          backgroundColor: 'rgba(79,172,254,0.5)',
                          borderRadius: 3,
                        }
                      }}>
                        {weatherData.forecastDays && weatherData.forecastDays.map((day, index) => (
                          <Box
                            key={index}
                              sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                p: 2,
                                borderRadius: 3,
                                bgcolor: index === 0 ? 'rgba(79,172,254,0.1)' : 'rgba(255,255,255,0.8)',
                                minWidth: 120,
                                border: '1px solid rgba(79,172,254,0.1)',
                                transition: 'all 0.2s ease',
                                '&:hover': {
                                  bgcolor: 'rgba(79,172,254,0.1)',
                                  transform: 'translateY(-5px)',
                                  boxShadow: '0 6px 20px rgba(79,172,254,0.2)'
                                }
                              }}
                            >
                              <Typography variant="body2" fontWeight={700} sx={{ color: '#1e293b' }}>
                                {index === 0 ? 'Today' : new Date(day.date).toLocaleDateString('en-US', { weekday: 'short' })}
                              </Typography>
                              <Typography variant="caption" sx={{ color: '#64748b', mb: 1 }}>
                                {new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                              </Typography>
                              {day.icon && (
                                <img
                                  src={`https://openweathermap.org/img/wn/${day.icon}.png`}
                                  alt={day.condition}
                                  style={{ width: 50, height: 50 }}
                                />
                              )}
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, my: 1 }}>
                                <Typography variant="body1" fontWeight={700} sx={{ color: '#1e293b' }}>
                                  {day.maxTemp}°
                                </Typography>
                                <Typography variant="body2" sx={{ color: '#64748b' }}>
                                  {day.minTemp}°
                                </Typography>
                              </Box>
                              <Typography variant="caption" sx={{ textTransform: 'capitalize', color: '#64748b', textAlign: 'center' }}>
                                {day.condition}
                              </Typography>

                              {/* Additional daily details */}
                              {day.precipitationProb > 20 && (
                                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                                  <span style={{ fontSize: '12px', marginRight: '4px' }}>🌧️</span>
                                  <Typography variant="caption" sx={{ fontSize: '11px' }}>
                                    {day.precipitationProb}%
                                  </Typography>
                                </Box>
                              )}
                            </Box>
                        ))}
                      </Box>
                    </Box>
                  </Box>

                  {/* Enhanced Weather Impact Analysis */}
                  <Box sx={{ mt: 3, p: 3, borderRadius: 3, bgcolor: 'rgba(255,255,255,0.8)', backdropFilter: 'blur(10px)', border: '1px solid rgba(79,172,254,0.1)' }}>
                    <Typography variant="h6" fontWeight={700} sx={{ mb: 3, color: '#1e293b' }}>
                      🏗️ Construction Impact Analysis
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12} md={4}>
                        <Card sx={{ p: 2, borderRadius: 2, bgcolor: 'rgba(79,172,254,0.05)', border: '1px solid rgba(79,172,254,0.1)' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <ConstructionIcon sx={{ mr: 1, color: getWorkSuitabilityColor(weatherData.workSuitability), fontSize: 24 }} />
                            <Typography variant="subtitle1" fontWeight={700}>
                              Work Conditions
                            </Typography>
                          </Box>
                          <Typography variant="h6" fontWeight={700} sx={{ color: getWorkSuitabilityColor(weatherData.workSuitability) }}>
                            {weatherData.workSuitability}
                          </Typography>
                          <Typography variant="body2" sx={{ color: '#64748b', mt: 1 }}>
                            Based on current weather conditions
                          </Typography>
                        </Card>
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <Card sx={{ p: 2, borderRadius: 2, bgcolor: 'rgba(240,147,251,0.05)', border: '1px solid rgba(240,147,251,0.1)' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <WarningIcon sx={{
                              mr: 1,
                              fontSize: 24,
                              color: dashboardMetrics.weatherImpact === 'high'
                                ? '#f44336'
                                : dashboardMetrics.weatherImpact === 'medium'
                                  ? '#ffc107'
                                  : '#4caf50'
                            }} />
                            <Typography variant="subtitle1" fontWeight={700}>
                              Project Risk
                            </Typography>
                          </Box>
                          <Typography variant="h6" fontWeight={700} sx={{
                            textTransform: 'capitalize',
                            color: dashboardMetrics.weatherImpact === 'high'
                              ? '#f44336'
                              : dashboardMetrics.weatherImpact === 'medium'
                                ? '#ffc107'
                                : '#4caf50'
                          }}>
                            {dashboardMetrics.weatherImpact} Impact
                          </Typography>
                          <Typography variant="body2" sx={{ color: '#64748b', mt: 1 }}>
                            Weather-related project delays
                          </Typography>
                        </Card>
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <Card sx={{ p: 2, borderRadius: 2, bgcolor: 'rgba(76,175,80,0.05)', border: '1px solid rgba(76,175,80,0.1)' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <InfoIcon sx={{ mr: 1, color: '#4caf50', fontSize: 24 }} />
                            <Typography variant="subtitle1" fontWeight={700}>
                              Recommendation
                            </Typography>
                          </Box>
                          <Typography variant="body1" fontWeight={600} sx={{ color: '#1e293b' }}>
                            {weatherData.workSuitability === 'Dangerous'
                              ? 'Suspend all outdoor work'
                              : weatherData.workSuitability === 'Poor'
                                ? 'Consider rescheduling outdoor work'
                                : weatherData.workSuitability === 'Moderate'
                                  ? 'Proceed with caution and safety measures'
                                  : 'Ideal conditions for all construction activities'
                            }
                          </Typography>
                        </Card>
                      </Grid>
                    </Grid>

                    {/* Additional Weather Insights */}
                    <Box sx={{ mt: 3, p: 2, borderRadius: 2, bgcolor: 'rgba(79,172,254,0.05)' }}>
                      <Typography variant="subtitle1" fontWeight={700} sx={{ mb: 2, color: '#1e293b' }}>
                        📊 Weather Insights for Construction
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6} md={3}>
                          <Box sx={{ textAlign: 'center' }}>
                            <Typography variant="caption" sx={{ color: '#64748b' }}>Temperature Range</Typography>
                            <Typography variant="body1" fontWeight={600}>
                              {weatherData.forecastDays?.[0]?.minTemp}° - {weatherData.forecastDays?.[0]?.maxTemp}°C
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                          <Box sx={{ textAlign: 'center' }}>
                            <Typography variant="caption" sx={{ color: '#64748b' }}>UV Index</Typography>
                            <Typography variant="body1" fontWeight={600}>
                              {weatherData.uvIndex || 'N/A'} {weatherData.uvIndex > 6 ? '(High)' : weatherData.uvIndex > 3 ? '(Moderate)' : '(Low)'}
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                          <Box sx={{ textAlign: 'center' }}>
                            <Typography variant="caption" sx={{ color: '#64748b' }}>Sunrise/Sunset</Typography>
                            <Typography variant="body1" fontWeight={600}>
                              {weatherData.sunrise ? new Date(weatherData.sunrise).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }) : 'N/A'} / {weatherData.sunset ? new Date(weatherData.sunset).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }) : 'N/A'}
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                          <Box sx={{ textAlign: 'center' }}>
                            <Typography variant="caption" sx={{ color: '#64748b' }}>Last Updated</Typography>
                            <Typography variant="body1" fontWeight={600}>
                              {weatherData.lastUpdated ? new Date(weatherData.lastUpdated).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }) : 'N/A'}
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Box>
          )}

        {/* Add Entry Dialog */}
        <PremiumDialog
          open={openDialog}
          onClose={() => setOpenDialog(false)}
          title="Add New Progress Entry"
          titleIcon="📊"
          maxWidth="lg"
          actions={
            <>
              <Button
                onClick={() => setOpenDialog(false)}
                sx={{
                  borderRadius: 2,
                  color: '#64748b',
                  borderColor: '#e2e8f0',
                  '&:hover': {
                    borderColor: '#cbd5e1',
                    backgroundColor: '#f8fafc',
                  }
                }}
                variant="outlined"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                variant="contained"
                sx={{
                  borderRadius: 2,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #764ba2 0%, #667eea 100%)',
                  },
                }}
              >
                Save Entry
              </Button>
            </>
          }
        >
          <Box sx={{ p: 2 }}>
            {/* 🎯 PREMIUM FORM PROGRESS STEPPER */}
            <Paper sx={{ p: 3, mb: 3, borderRadius: 4, background: 'linear-gradient(135deg, rgba(102,126,234,0.05) 0%, rgba(118,75,162,0.05) 100%)', border: '1px solid rgba(102,126,234,0.1)' }}>
              <Stepper activeStep={0} alternativeLabel sx={{ mb: 2 }}>
                <Step>
                  <StepLabel>📋 Basic Info</StepLabel>
                </Step>
                <Step>
                  <StepLabel>🔵 Network Details</StepLabel>
                </Step>
                <Step>
                  <StepLabel>📊 Progress & Labour</StepLabel>
                </Step>
                <Step>
                  <StepLabel>✅ Review & Submit</StepLabel>
                </Step>
              </Stepper>

              {/* 🤖 AI SUGGESTIONS */}
              {aiSuggestions.length > 0 && (
                <Alert
                  severity="info"
                  sx={{
                    mb: 2,
                    borderRadius: 3,
                    background: 'linear-gradient(135deg, rgba(79,172,254,0.1) 0%, rgba(0,242,254,0.1) 100%)',
                    border: '1px solid rgba(79,172,254,0.2)'
                  }}
                >
                  <AlertTitle sx={{ fontWeight: 600 }}>🤖 AI Suggestions</AlertTitle>
                  <Box component="ul" sx={{ m: 0, pl: 2 }}>
                    {aiSuggestions.slice(0, 2).map((suggestion, index) => (
                      <Typography component="li" variant="body2" key={index} sx={{ mb: 0.5 }}>
                        {suggestion}
                      </Typography>
                    ))}
                  </Box>
                </Alert>
              )}

              {/* 🌤️ WEATHER CONDITIONS */}
              {renderWeatherSection()}

              {/* 👥 COLLABORATORS */}
              <Box sx={{
                p: 2,
                borderRadius: 3,
                background: 'linear-gradient(135deg, rgba(240,147,251,0.1) 0%, rgba(245,87,108,0.1) 100%)',
                border: '1px solid rgba(240,147,251,0.2)',
                mb: 2
              }}>
                <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, color: '#1e293b' }}>
                  👥 Active Collaborators
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {collaborators.map((collaborator) => (
                    <Chip
                      key={collaborator.id}
                      avatar={
                        <Avatar sx={{
                          width: 24,
                          height: 24,
                          fontSize: '0.75rem',
                          background: collaborator.online ? 'linear-gradient(135deg, #4ade80 0%, #22c55e 100%)' : '#94a3b8'
                        }}>
                          {collaborator.avatar}
                        </Avatar>
                      }
                      label={`${collaborator.name} (${collaborator.role})`}
                      size="small"
                      sx={{
                        borderRadius: 2,
                        background: 'rgba(255,255,255,0.8)',
                        '& .MuiChip-label': { fontSize: '0.75rem' }
                      }}
                    />
                  ))}
                </Box>
              </Box>
            </Paper>

            {/* Basic Information Section */}
            <Paper sx={{ p: 3, mb: 3, borderRadius: 3, border: '1px solid #e2e8f0' }}>
              <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
                📋 Basic Information
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    label="Date"
                    type="date"
                    value={formData.date}
                    onChange={(e) => handleInputChange('date', e.target.value)}
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    label="S.No"
                    value={formData.sNo}
                    onChange={(e) => handleInputChange('sNo', e.target.value)}
                    fullWidth
                    placeholder="Auto-generated if empty"
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    label="Contractor Name *"
                    value={formData.contractorName}
                    onChange={(e) => handleInputChange('contractorName', e.target.value)}
                    fullWidth
                    required
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    label="Node"
                    value={formData.node}
                    onChange={(e) => handleInputChange('node', e.target.value)}
                    fullWidth
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    label="Pipe Diameter (MM)"
                    value={formData.pipeDiameter}
                    onChange={(e) => handleInputChange('pipeDiameter', e.target.value)}
                    fullWidth
                    type="number"
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    select
                    label="Units"
                    value={formData.units}
                    onChange={(e) => handleInputChange('units', e.target.value)}
                    fullWidth
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  >
                    <MenuItem value="RMT">RMT</MenuItem>
                    <MenuItem value="Kms">Kms</MenuItem>
                    <MenuItem value="Nos">Nos</MenuItem>
                    <MenuItem value="Cum">Cum</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    label="Location *"
                    value={formData.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    fullWidth
                    required
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    label="Engineer/Supervisor"
                    value={formData.engineerSupervisor}
                    onChange={(e) => handleInputChange('engineerSupervisor', e.target.value)}
                    fullWidth
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
              </Grid>
            </Paper>

            {/* HDPE Network Line Section */}
            <Paper sx={{ p: 3, mb: 3, borderRadius: 3, border: '1px solid #e2e8f0' }}>
              <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
                🔵 HDPE Network Line
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={4}>
                  <TextField
                    label="Excavation"
                    value={formData.hdpeExcavation}
                    onChange={(e) => handleInputChange('hdpeExcavation', parseFloat(e.target.value) || 0)}
                    fullWidth
                    type="number"
                    InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    label="Pipe Laying"
                    value={formData.hdpePipeLaying}
                    onChange={(e) => handleInputChange('hdpePipeLaying', parseFloat(e.target.value) || 0)}
                    fullWidth
                    type="number"
                    InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    label="Back Filling"
                    value={formData.hdpeBackFilling}
                    onChange={(e) => handleInputChange('hdpeBackFilling', parseFloat(e.target.value) || 0)}
                    fullWidth
                    type="number"
                    InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
              </Grid>
            </Paper>

            {/* MS Network Line Section */}
            <Paper sx={{ p: 3, mb: 3, borderRadius: 3, border: '1px solid #e2e8f0' }}>
              <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
                🔶 MS Network Line
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={4}>
                  <TextField
                    label="Excavation"
                    value={formData.msExcavation}
                    onChange={(e) => handleInputChange('msExcavation', parseFloat(e.target.value) || 0)}
                    fullWidth
                    type="number"
                    InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    label="Pipe Laying"
                    value={formData.msPipeLaying}
                    onChange={(e) => handleInputChange('msPipeLaying', parseFloat(e.target.value) || 0)}
                    fullWidth
                    type="number"
                    InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    label="Back Filling"
                    value={formData.msBackFilling}
                    onChange={(e) => handleInputChange('msBackFilling', parseFloat(e.target.value) || 0)}
                    fullWidth
                    type="number"
                    InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
              </Grid>
            </Paper>

            {/* Work Description Section */}
            <Paper sx={{ p: 3, mb: 3, borderRadius: 3, border: '1px solid #e2e8f0' }}>
              <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
                📝 Work Description & Agency
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={8}>
                  <TextField
                    label="Work Description *"
                    value={formData.workDescription}
                    onChange={(e) => handleInputChange('workDescription', e.target.value)}
                    fullWidth
                    required
                    multiline
                    rows={4}
                    placeholder="e.g., MS & HDPE Pipe Network Line Excavation & Laying work in Progress..."
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    label="Agency Name *"
                    value={formData.agencyName}
                    onChange={(e) => handleInputChange('agencyName', e.target.value)}
                    fullWidth
                    required
                    placeholder="e.g., MANTENA INFRASOL PVT LTD"
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    label="Remarks"
                    value={formData.remarks}
                    onChange={(e) => handleInputChange('remarks', e.target.value)}
                    fullWidth
                    multiline
                    rows={2}
                    placeholder="Additional remarks or notes..."
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
              </Grid>
            </Paper>

            {/* Summary Section */}
            <Paper sx={{ p: 3, mb: 3, borderRadius: 3, border: '1px solid #e2e8f0' }}>
              <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
                📊 Progress Summary
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    label="Target Quantity"
                    value={formData.targetQty}
                    onChange={(e) => handleInputChange('targetQty', parseFloat(e.target.value) || 0)}
                    fullWidth
                    type="number"
                    InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    label="Last Season Quantity"
                    value={formData.lastSeasonQty}
                    onChange={(e) => handleInputChange('lastSeasonQty', parseFloat(e.target.value) || 0)}
                    fullWidth
                    type="number"
                    InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    label="This Season Quantity"
                    value={formData.thisSeasonQty}
                    onChange={(e) => handleInputChange('thisSeasonQty', parseFloat(e.target.value) || 0)}
                    fullWidth
                    type="number"
                    InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    label="Cumulative Quantity"
                    value={formData.cumulativeQty}
                    onChange={(e) => handleInputChange('cumulativeQty', parseFloat(e.target.value) || 0)}
                    fullWidth
                    type="number"
                    InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    label="Balance Quantity"
                    value={formData.balanceQty}
                    fullWidth
                    type="number"
                    InputProps={{ readOnly: true }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        backgroundColor: '#f8fafc'
                      }
                    }}
                    helperText="Auto-calculated: Target - Cumulative"
                  />
                </Grid>
              </Grid>
            </Paper>

            {/* Labour Details Section */}
            <Paper sx={{ p: 3, mb: 3, borderRadius: 3, border: '1px solid #e2e8f0' }}>
              <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
                👷 Labour Details
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={4}>
                  <TextField
                    label="Skilled Labour"
                    value={formData.skilledLabour}
                    onChange={(e) => handleInputChange('skilledLabour', parseInt(e.target.value) || 0)}
                    fullWidth
                    type="number"
                    InputProps={{ inputProps: { min: 0 } }}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    label="Unskilled Labour"
                    value={formData.unskilledLabour}
                    onChange={(e) => handleInputChange('unskilledLabour', parseInt(e.target.value) || 0)}
                    fullWidth
                    type="number"
                    InputProps={{ inputProps: { min: 0 } }}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    label="Total Labour"
                    value={formData.totalLabour}
                    fullWidth
                    type="number"
                    InputProps={{ readOnly: true }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        backgroundColor: '#f8fafc'
                      }
                    }}
                    helperText="Auto-calculated: Skilled + Unskilled"
                  />
                </Grid>
              </Grid>
            </Paper>

            {/* Additional Details Section */}
            <Paper sx={{ p: 3, mb: 3, borderRadius: 3, border: '1px solid #e2e8f0' }}>
              <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
                🔧 Additional Details
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    label="Concrete Quantity (Cum)"
                    value={formData.concreteQty}
                    onChange={(e) => handleInputChange('concreteQty', parseFloat(e.target.value) || 0)}
                    fullWidth
                    type="number"
                    InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    label="Machinery Details"
                    value={formData.machineryDetails}
                    onChange={(e) => handleInputChange('machineryDetails', e.target.value)}
                    fullWidth
                    placeholder="e.g., Excavator, Dumper, ROC Machine"
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                  />
                </Grid>
              </Grid>
            </Paper>

            {/* 🌟 PREMIUM QUALITY & SAFETY ASSESSMENT */}
            <Paper sx={{
              p: 3,
              mb: 3,
              borderRadius: 3,
              border: '1px solid #e2e8f0',
              background: 'linear-gradient(135deg, rgba(250,112,154,0.05) 0%, rgba(254,225,64,0.05) 100%)'
            }}>
              <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
                🌟 Quality & Safety Assessment
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" sx={{ mb: 2, color: '#1e293b', fontWeight: 600 }}>
                    ⭐ Work Quality Rating
                  </Typography>
                  <Rating
                    value={qualityRating}
                    onChange={(event, newValue) => setQualityRating(newValue)}
                    precision={0.5}
                    size="large"
                    sx={{ mb: 2 }}
                  />
                  <Typography variant="body2" sx={{ color: '#64748b' }}>
                    Rate the overall quality of work completed today
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" sx={{ mb: 2, color: '#1e293b', fontWeight: 600 }}>
                    🛡️ Safety Score (%)
                  </Typography>
                  <Slider
                    value={safetyScore}
                    onChange={(event, newValue) => setSafetyScore(newValue)}
                    valueLabelDisplay="on"
                    min={0}
                    max={100}
                    marks={[
                      { value: 0, label: '0%' },
                      { value: 50, label: '50%' },
                      { value: 100, label: '100%' }
                    ]}
                    sx={{
                      color: safetyScore >= 90 ? '#22c55e' : safetyScore >= 70 ? '#f59e0b' : '#ef4444',
                      '& .MuiSlider-thumb': {
                        background: safetyScore >= 90 ? 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)' :
                                   safetyScore >= 70 ? 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)' :
                                   'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)'
                      }
                    }}
                  />
                  <Typography variant="body2" sx={{ color: '#64748b', mt: 1 }}>
                    Assess safety compliance and practices
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" sx={{ mb: 2, color: '#1e293b', fontWeight: 600 }}>
                    ✅ Compliance Checklist
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6} md={3}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={complianceChecks.safetyCompliance}
                            onChange={(e) => setComplianceChecks({
                              ...complianceChecks,
                              safetyCompliance: e.target.checked
                            })}
                            color="success"
                          />
                        }
                        label="Safety Compliance"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={complianceChecks.environmentalCompliance}
                            onChange={(e) => setComplianceChecks({
                              ...complianceChecks,
                              environmentalCompliance: e.target.checked
                            })}
                            color="success"
                          />
                        }
                        label="Environmental"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={complianceChecks.qualityStandards}
                            onChange={(e) => setComplianceChecks({
                              ...complianceChecks,
                              qualityStandards: e.target.checked
                            })}
                            color="success"
                          />
                        }
                        label="Quality Standards"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={complianceChecks.regulatoryApproval}
                            onChange={(e) => setComplianceChecks({
                              ...complianceChecks,
                              regulatoryApproval: e.target.checked
                            })}
                            color="success"
                          />
                        }
                        label="Regulatory"
                      />
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Paper>

            {/* 📎 PREMIUM ATTACHMENTS & MEDIA */}
            <Paper sx={{
              p: 3,
              mb: 3,
              borderRadius: 3,
              border: '1px solid #e2e8f0',
              background: 'linear-gradient(135deg, rgba(79,172,254,0.05) 0%, rgba(0,242,254,0.05) 100%)'
            }}>
              <Typography variant="h6" sx={{ mb: 3, color: '#1e293b', fontWeight: 600 }}>
                📎 Attachments & Media
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={4}>
                  <Button
                    variant="outlined"
                    startIcon={<PhotoIcon />}
                    fullWidth
                    sx={{
                      borderRadius: 3,
                      borderColor: '#4facfe',
                      color: '#4facfe',
                      borderStyle: 'dashed',
                      height: 80,
                      '&:hover': {
                        borderColor: '#00f2fe',
                        backgroundColor: 'rgba(79,172,254,0.05)'
                      }
                    }}
                  >
                    Add Photos
                  </Button>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Button
                    variant="outlined"
                    startIcon={<AttachFileIcon />}
                    fullWidth
                    sx={{
                      borderRadius: 3,
                      borderColor: '#f093fb',
                      color: '#f093fb',
                      borderStyle: 'dashed',
                      height: 80,
                      '&:hover': {
                        borderColor: '#f5576c',
                        backgroundColor: 'rgba(240,147,251,0.05)'
                      }
                    }}
                  >
                    Attach Files
                  </Button>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Button
                    variant="outlined"
                    startIcon={<MapIcon />}
                    fullWidth
                    sx={{
                      borderRadius: 3,
                      borderColor: '#fa709a',
                      color: '#fa709a',
                      borderStyle: 'dashed',
                      height: 80,
                      '&:hover': {
                        borderColor: '#fee140',
                        backgroundColor: 'rgba(250,112,154,0.05)'
                      }
                    }}
                  >
                    GPS Location
                  </Button>
                </Grid>
              </Grid>
            </Paper>

            {/* Form Summary */}
            <Paper sx={{ p: 3, borderRadius: 3, border: '1px solid #e2e8f0', backgroundColor: '#f8fafc' }}>
              <Typography variant="body2" sx={{ color: '#64748b', mb: 2 }}>
                📌 <strong>Required Fields:</strong> Contractor Name, Location, Work Description, Agency Name
              </Typography>
              <Typography variant="body2" sx={{ color: '#64748b' }}>
                💡 <strong>Note:</strong> Balance Quantity and Total Labour are auto-calculated based on your inputs.
              </Typography>
            </Paper>
          </Box>
        </PremiumDialog>

        {/* 🔍 ADVANCED FILTERS DIALOG */}
        <PremiumDialog
          open={openAdvancedFilters}
          onClose={() => setOpenAdvancedFilters(false)}
          title="🔍 Advanced Filters"
          maxWidth="md"
        >
          <Box sx={{ p: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="📅 Start Date"
                  type="date"
                  value={advancedFilters.dateRange.start || ''}
                  onChange={(e) => setAdvancedFilters({
                    ...advancedFilters,
                    dateRange: { ...advancedFilters.dateRange, start: e.target.value }
                  })}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="📅 End Date"
                  type="date"
                  value={advancedFilters.dateRange.end || ''}
                  onChange={(e) => setAdvancedFilters({
                    ...advancedFilters,
                    dateRange: { ...advancedFilters.dateRange, end: e.target.value }
                  })}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  select
                  label="📍 Location Filter"
                  value={advancedFilters.location}
                  onChange={(e) => setAdvancedFilters({
                    ...advancedFilters,
                    location: e.target.value
                  })}
                  fullWidth
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                >
                  <MenuItem value="">All Locations</MenuItem>
                  {uniqueLocations.map((location) => (
                    <MenuItem key={location} value={location}>
                      {location}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  select
                  label="🏗️ Contractor Filter"
                  value={advancedFilters.contractor}
                  onChange={(e) => setAdvancedFilters({
                    ...advancedFilters,
                    contractor: e.target.value
                  })}
                  fullWidth
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                >
                  <MenuItem value="">All Contractors</MenuItem>
                  {uniqueContractors.map((contractor) => (
                    <MenuItem key={contractor} value={contractor}>
                      {contractor}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2" sx={{ mb: 2, color: '#1e293b', fontWeight: 600 }}>
                  📊 Progress Range: {advancedFilters.progress[0]}% - {advancedFilters.progress[1]}%
                </Typography>
                <Slider
                  value={advancedFilters.progress}
                  onChange={(event, newValue) => setAdvancedFilters({
                    ...advancedFilters,
                    progress: newValue
                  })}
                  valueLabelDisplay="on"
                  min={0}
                  max={100}
                  marks={[
                    { value: 0, label: '0%' },
                    { value: 50, label: '50%' },
                    { value: 100, label: '100%' }
                  ]}
                  sx={{ color: '#667eea' }}
                />
              </Grid>
            </Grid>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 3 }}>
              <Button
                onClick={() => {
                  setAdvancedFilters({
                    dateRange: { start: null, end: null },
                    contractor: '',
                    location: '',
                    status: 'all',
                    priority: 'all',
                    progress: [0, 100],
                    workType: 'all',
                    qualityRating: [0, 5]
                  });
                }}
                variant="outlined"
                sx={{ borderRadius: 2 }}
              >
                Clear Filters
              </Button>
              <Button
                onClick={() => {
                  setOpenAdvancedFilters(false);
                  setSnackbar({
                    open: true,
                    message: 'Advanced filters applied successfully!',
                    severity: 'success'
                  });
                }}
                variant="contained"
                sx={{
                  borderRadius: 2,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #764ba2 0%, #667eea 100%)',
                  },
                }}
              >
                Apply Filters
              </Button>
            </Box>
          </Box>
        </PremiumDialog>

        {/* 🚀 PREMIUM FLOATING ACTION MENU */}
        <SpeedDial
          ariaLabel="Quick Actions"
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
            '& .MuiFab-primary': {
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              boxShadow: '0 8px 32px rgba(102,126,234,0.4)',
              '&:hover': {
                background: 'linear-gradient(135deg, #764ba2 0%, #667eea 100%)',
                transform: 'scale(1.1)',
                boxShadow: '0 12px 40px rgba(102,126,234,0.5)'
              }
            }
          }}
          icon={<SpeedDialIcon />}
          direction="up"
        >
          <SpeedDialAction
            icon={<AddIcon />}
            tooltipTitle="Add New DPR"
            onClick={() => setOpenDialog(true)}
            sx={{
              '& .MuiFab-primary': {
                background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                '&:hover': { transform: 'scale(1.1)' }
              }
            }}
          />
          <SpeedDialAction
            icon={<AnalyticsIcon />}
            tooltipTitle="View Analytics"
            onClick={() => setCurrentTab(2)}
            sx={{
              '& .MuiFab-primary': {
                background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                '&:hover': { transform: 'scale(1.1)' }
              }
            }}
          />
          <SpeedDialAction
            icon={<ExportIcon />}
            tooltipTitle="Export Reports"
            onClick={() => {
              setSnackbar({
                open: true,
                message: 'Export feature coming soon!',
                severity: 'info'
              });
            }}
            sx={{
              '& .MuiFab-primary': {
                background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                '&:hover': { transform: 'scale(1.1)' }
              }
            }}
          />
          <SpeedDialAction
            icon={<NotificationsIcon />}
            tooltipTitle="Notifications"
            onClick={() => {
              setSnackbar({
                open: true,
                message: `You have ${notifications.length} new notifications`,
                severity: 'info'
              });
            }}
            sx={{
              '& .MuiFab-primary': {
                background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                '&:hover': { transform: 'scale(1.1)' }
              }
            }}
          />
        </SpeedDial>

        {/* 🎉 PREMIUM SNACKBAR NOTIFICATIONS */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={4000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <Alert
            onClose={() => setSnackbar({ ...snackbar, open: false })}
            severity={snackbar.severity}
            sx={{
              borderRadius: 3,
              boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
              '& .MuiAlert-icon': { fontSize: 24 }
            }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>

        {/* 🎯 PREMIUM LOADING OVERLAY */}
        {loading && (
          <Box
            sx={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'rgba(15, 23, 42, 0.8)',
              backdropFilter: 'blur(8px)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 9999
            }}
          >
            <Box sx={{ textAlign: 'center', color: 'white' }}>
              <CircularProgress
                size={60}
                sx={{
                  color: '#667eea',
                  mb: 2
                }}
              />
              <Typography variant="h6" fontWeight={600}>
                Processing your request...
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.8, mt: 1 }}>
                Please wait while we update your data
              </Typography>
            </Box>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default DailyProgressReport;
