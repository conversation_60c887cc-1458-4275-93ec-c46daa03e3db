"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Events
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaListInstance = exports.SchemaInstance = exports.SchemaContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
const schemaVersion_1 = require("./schema/schemaVersion");
class SchemaContextImpl {
    constructor(_version, id) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(id)) {
            throw new Error("Parameter 'id' is not valid.");
        }
        this._solution = { id };
        this._uri = `/Schemas/${id}`;
    }
    get versions() {
        this._versions =
            this._versions ||
                (0, schemaVersion_1.SchemaVersionListInstance)(this._version, this._solution.id);
        return this._versions;
    }
    fetch(callback) {
        const headers = {};
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            headers,
        });
        operationPromise = operationPromise.then((payload) => new SchemaInstance(operationVersion, payload, instance._solution.id));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.SchemaContextImpl = SchemaContextImpl;
class SchemaInstance {
    constructor(_version, payload, id) {
        this._version = _version;
        this.id = payload.id;
        this.url = payload.url;
        this.links = payload.links;
        this.latestVersionDateCreated = deserialize.iso8601DateTime(payload.latest_version_date_created);
        this.latestVersion = deserialize.integer(payload.latest_version);
        this._solution = { id: id || this.id };
    }
    get _proxy() {
        this._context =
            this._context || new SchemaContextImpl(this._version, this._solution.id);
        return this._context;
    }
    /**
     * Fetch a SchemaInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed SchemaInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Access the versions.
     */
    versions() {
        return this._proxy.versions;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            id: this.id,
            url: this.url,
            links: this.links,
            latestVersionDateCreated: this.latestVersionDateCreated,
            latestVersion: this.latestVersion,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.SchemaInstance = SchemaInstance;
function SchemaListInstance(version) {
    const instance = ((id) => instance.get(id));
    instance.get = function get(id) {
        return new SchemaContextImpl(version, id);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.SchemaListInstance = SchemaListInstance;
