"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "default", {
  enumerable: true,
  get: function () {
    return _useThemeProps.default;
  }
});
Object.defineProperty(exports, "getThemeProps", {
  enumerable: true,
  get: function () {
    return _getThemeProps.default;
  }
});
var _useThemeProps = _interopRequireDefault(require("./useThemeProps"));
var _getThemeProps = _interopRequireDefault(require("./getThemeProps"));